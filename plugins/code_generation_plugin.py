"""
Code Generation Plugin

Provides code generation capabilities including template-based generation,
boilerplate creation, and intelligent code completion.
"""

import ast
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
from textwrap import dedent

from agent_framework.core.types import (
    PluginInterface, PluginRequest, PluginResponse, PluginCapability
)


class CodeGenerationPlugin(PluginInterface):
    """
    Plugin for generating code based on specifications and templates.

    Provides capabilities for:
    - Template-based code generation
    - Boilerplate creation for common patterns
    - Function and class generation
    - Test case generation
    - Documentation generation
    """

    PLUGIN_NAME = "code_generation"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Generates code from templates and specifications"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []

    def __init__(self):
        """Initialize the code generation plugin."""
        self._is_initialized = False
        self._config = {}
        self._templates = {}
        self._load_default_templates()

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        start_time = time.time()

        try:
            capability = request.capability
            parameters = request.parameters

            if capability == "generate_function":
                result = await self._generate_function(parameters)
            elif capability == "generate_class":
                result = await self._generate_class(parameters)
            elif capability == "generate_boilerplate":
                result = await self._generate_boilerplate(parameters)
            elif capability == "generate_tests":
                result = await self._generate_tests(parameters)
            elif capability == "generate_from_template":
                result = await self._generate_from_template(parameters)
            elif capability == "complete_code":
                result = await self._complete_code(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "capability": capability,
                    "parameters": parameters
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return [
            PluginCapability(
                name="generate_function",
                description="Generate a function based on specification",
                input_schema={
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "Function name"},
                        "description": {"type": "string", "description": "Function description"},
                        "parameters": {"type": "array", "description": "Function parameters"},
                        "return_type": {"type": "string", "description": "Return type"},
                        "docstring_style": {"type": "string", "enum": ["google", "numpy", "sphinx"], "default": "google"}
                    },
                    "required": ["name", "description"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "imports": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="generate_class",
                description="Generate a class based on specification",
                input_schema={
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "Class name"},
                        "description": {"type": "string", "description": "Class description"},
                        "attributes": {"type": "array", "description": "Class attributes"},
                        "methods": {"type": "array", "description": "Class methods"},
                        "inheritance": {"type": "array", "description": "Parent classes"},
                        "design_pattern": {"type": "string", "enum": ["singleton", "factory", "observer", "builder"]}
                    },
                    "required": ["name", "description"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "imports": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="generate_boilerplate",
                description="Generate boilerplate code for common patterns",
                input_schema={
                    "type": "object",
                    "properties": {
                        "type": {"type": "string", "enum": ["flask_app", "fastapi_app", "cli_app", "pytest_test", "django_model"]},
                        "name": {"type": "string", "description": "Project/module name"},
                        "features": {"type": "array", "description": "Additional features to include"}
                    },
                    "required": ["type", "name"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "files": {"type": "object", "description": "Generated files with their content"},
                        "dependencies": {"type": "array", "description": "Required dependencies"}
                    }
                },
                supported_languages=["python"]
            )
        ]

    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self._config.clear()
        self._templates.clear()

    def _load_default_templates(self) -> None:
        """Load default code templates."""
        self._templates = {
            "function": dedent("""
                def {name}({parameters}){return_annotation}:
                    \"\"\"{docstring}\"\"\"
                    {body}
            """).strip(),
            
            "class": dedent("""
                class {name}{inheritance}:
                    \"\"\"{docstring}\"\"\"
                    
                    def __init__(self{init_params}):
                        \"\"\"{init_docstring}\"\"\"
                        {init_body}
                    
                    {methods}
            """).strip(),
            
            "flask_app": dedent("""
                from flask import Flask, request, jsonify
                
                app = Flask(__name__)
                
                @app.route('/')
                def hello():
                    return jsonify({{"message": "Hello from {name}!"}})
                
                if __name__ == '__main__':
                    app.run(debug=True)
            """).strip(),
            
            "fastapi_app": dedent("""
                from fastapi import FastAPI
                from pydantic import BaseModel
                
                app = FastAPI(title="{name}")
                
                class Item(BaseModel):
                    name: str
                    description: str = None
                
                @app.get("/")
                async def root():
                    return {{"message": "Hello from {name}!"}}
                
                @app.post("/items/")
                async def create_item(item: Item):
                    return item
            """).strip()
        }

    async def _generate_function(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a function based on specification."""
        name = parameters.get("name", "new_function")
        description = parameters.get("description", "")
        params = parameters.get("parameters", [])
        return_type = parameters.get("return_type", "")
        docstring_style = parameters.get("docstring_style", "google")

        # Build parameter string
        param_strings = []
        for param in params:
            if isinstance(param, dict):
                param_name = param.get("name", "arg")
                param_type = param.get("type", "")
                default_value = param.get("default", None)

                param_str = param_name
                if param_type:
                    param_str += f": {param_type}"
                if default_value is not None:
                    param_str += f" = {repr(default_value)}"
                param_strings.append(param_str)
            else:
                param_strings.append(str(param))

        parameters_str = ", ".join(param_strings)
        return_annotation = f" -> {return_type}" if return_type else ""

        # Generate docstring
        docstring = self._generate_docstring(description, params, return_type, docstring_style)

        # Generate basic function body
        body = "pass  # TODO: Implement function logic"
        if return_type and return_type != "None":
            if return_type in ["str", "string"]:
                body = 'return ""'
            elif return_type in ["int", "integer"]:
                body = "return 0"
            elif return_type in ["list", "List"]:
                body = "return []"
            elif return_type in ["dict", "Dict"]:
                body = "return {}"
            elif return_type in ["bool", "boolean"]:
                body = "return False"

        code = self._templates["function"].format(
            name=name,
            parameters=parameters_str,
            return_annotation=return_annotation,
            docstring=docstring,
            body=body
        )

        # Determine required imports
        imports = []
        if any("List" in str(p) for p in params) or "List" in return_type:
            imports.append("from typing import List")
        if any("Dict" in str(p) for p in params) or "Dict" in return_type:
            imports.append("from typing import Dict")
        if any("Optional" in str(p) for p in params) or "Optional" in return_type:
            imports.append("from typing import Optional")

        return {
            "code": code,
            "imports": imports,
            "metadata": {
                "function_name": name,
                "parameter_count": len(params),
                "has_return_type": bool(return_type)
            }
        }

    async def _generate_class(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a class based on specification."""
        name = parameters.get("name", "NewClass")
        description = parameters.get("description", "")
        attributes = parameters.get("attributes", [])
        methods = parameters.get("methods", [])
        inheritance = parameters.get("inheritance", [])
        design_pattern = parameters.get("design_pattern", None)

        # Build inheritance string
        inheritance_str = ""
        if inheritance:
            inheritance_str = f"({', '.join(inheritance)})"

        # Generate init parameters and body
        init_params = ""
        init_body = ""
        init_docstring = f"Initialize {name} instance."

        if attributes:
            init_param_strings = []
            init_body_lines = []

            for attr in attributes:
                if isinstance(attr, dict):
                    attr_name = attr.get("name", "attr")
                    attr_type = attr.get("type", "")
                    default_value = attr.get("default", None)

                    param_str = attr_name
                    if attr_type:
                        param_str += f": {attr_type}"
                    if default_value is not None:
                        param_str += f" = {repr(default_value)}"

                    init_param_strings.append(param_str)
                    init_body_lines.append(f"        self.{attr_name} = {attr_name}")
                else:
                    attr_name = str(attr)
                    init_param_strings.append(attr_name)
                    init_body_lines.append(f"        self.{attr_name} = {attr_name}")

            if init_param_strings:
                init_params = ", " + ", ".join(init_param_strings)
            init_body = "\n".join(init_body_lines) if init_body_lines else "        pass"

        # Generate methods
        method_strings = []
        for method in methods:
            if isinstance(method, dict):
                method_code = await self._generate_function(method)
                # Convert function to method (add self parameter)
                method_code_str = method_code["code"]
                method_code_str = method_code_str.replace("def ", "    def ", 1)
                if not method_code_str.startswith("    def __"):
                    # Add self parameter if not already present
                    method_code_str = re.sub(
                        r"def (\w+)\(",
                        r"def \1(self, ",
                        method_code_str
                    )
                method_strings.append(method_code_str)

        methods_str = "\n\n".join(method_strings) if method_strings else "    pass"

        # Apply design pattern if specified
        if design_pattern == "singleton":
            methods_str = self._add_singleton_pattern(methods_str)

        code = self._templates["class"].format(
            name=name,
            inheritance=inheritance_str,
            docstring=description,
            init_params=init_params,
            init_docstring=init_docstring,
            init_body=init_body or "        pass",
            methods=methods_str
        )

        # Determine required imports
        imports = []
        if design_pattern == "singleton":
            imports.append("from typing import Optional")

        return {
            "code": code,
            "imports": imports,
            "metadata": {
                "class_name": name,
                "attribute_count": len(attributes),
                "method_count": len(methods),
                "design_pattern": design_pattern
            }
        }

    async def _generate_boilerplate(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate boilerplate code for common patterns."""
        boilerplate_type = parameters.get("type", "")
        name = parameters.get("name", "MyProject")
        features = parameters.get("features", [])

        files = {}
        dependencies = []

        if boilerplate_type == "flask_app":
            files["app.py"] = self._templates["flask_app"].format(name=name)
            dependencies = ["flask"]

            if "database" in features:
                files["models.py"] = self._generate_flask_models()
                dependencies.append("flask-sqlalchemy")

            if "auth" in features:
                files["auth.py"] = self._generate_flask_auth()
                dependencies.append("flask-login")

        elif boilerplate_type == "fastapi_app":
            files["main.py"] = self._templates["fastapi_app"].format(name=name)
            dependencies = ["fastapi", "uvicorn"]

            if "database" in features:
                files["models.py"] = self._generate_fastapi_models()
                dependencies.extend(["sqlalchemy", "databases"])

        elif boilerplate_type == "cli_app":
            files["cli.py"] = self._generate_cli_app(name)
            dependencies = ["click"]

        elif boilerplate_type == "pytest_test":
            files["test_main.py"] = self._generate_pytest_template(name)
            dependencies = ["pytest"]

        return {
            "files": files,
            "dependencies": dependencies,
            "metadata": {
                "type": boilerplate_type,
                "name": name,
                "features": features
            }
        }

    async def _generate_tests(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test cases for given code."""
        code = parameters.get("code", "")
        test_framework = parameters.get("framework", "pytest")

        if not code:
            raise ValueError("Code parameter is required for test generation")

        # Parse the code to extract functions and classes
        try:
            tree = ast.parse(code)
            test_generator = TestGenerator(test_framework)
            test_generator.visit(tree)

            return {
                "test_code": test_generator.generate_tests(),
                "test_count": test_generator.test_count,
                "imports": test_generator.get_imports()
            }
        except SyntaxError as e:
            raise ValueError(f"Invalid Python code: {e}")

    async def _generate_from_template(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate code from a custom template."""
        template = parameters.get("template", "")
        variables = parameters.get("variables", {})

        if not template:
            raise ValueError("Template parameter is required")

        try:
            code = template.format(**variables)
            return {
                "code": code,
                "variables_used": list(variables.keys())
            }
        except KeyError as e:
            raise ValueError(f"Missing template variable: {e}")

    async def _complete_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Complete partial code based on context."""
        partial_code = parameters.get("code", "")
        context = parameters.get("context", "")

        # Simple code completion logic
        suggestions = []

        if partial_code.endswith("."):
            # Method/attribute completion
            suggestions = ["append(", "extend(", "insert(", "remove(", "pop("]
        elif "def " in partial_code and not partial_code.strip().endswith(":"):
            # Function definition completion
            suggestions = ["):"]
        elif "class " in partial_code and not partial_code.strip().endswith(":"):
            # Class definition completion
            suggestions = [":"]
        elif partial_code.strip().endswith("import"):
            # Import completion
            suggestions = ["os", "sys", "json", "re", "datetime", "pathlib"]

        return {
            "suggestions": suggestions,
            "completed_code": partial_code,
            "context_used": bool(context)
        }

    def _generate_docstring(self, description: str, params: List, return_type: str, style: str = "google") -> str:
        """Generate docstring in specified style."""
        if style == "google":
            lines = [description]
            if params:
                lines.append("")
                lines.append("Args:")
                for param in params:
                    if isinstance(param, dict):
                        param_name = param.get("name", "arg")
                        param_desc = param.get("description", "Parameter description")
                        param_type = param.get("type", "")
                        type_str = f" ({param_type})" if param_type else ""
                        lines.append(f"    {param_name}{type_str}: {param_desc}")
                    else:
                        lines.append(f"    {param}: Parameter description")

            if return_type and return_type != "None":
                lines.append("")
                lines.append("Returns:")
                lines.append(f"    {return_type}: Return value description")

            return "\n    ".join(lines)

        # Default to simple description
        return description

    def _add_singleton_pattern(self, methods_str: str) -> str:
        """Add singleton pattern implementation to class methods."""
        singleton_methods = dedent("""
            _instance: Optional['ClassName'] = None
            _lock = threading.Lock()

            def __new__(cls, *args, **kwargs):
                if cls._instance is None:
                    with cls._lock:
                        if cls._instance is None:
                            cls._instance = super().__new__(cls)
                return cls._instance
        """).strip()

        return singleton_methods + "\n\n" + methods_str

    def _generate_flask_models(self) -> str:
        """Generate Flask SQLAlchemy models."""
        return dedent("""
            from flask_sqlalchemy import SQLAlchemy
            from datetime import datetime

            db = SQLAlchemy()

            class User(db.Model):
                id = db.Column(db.Integer, primary_key=True)
                username = db.Column(db.String(80), unique=True, nullable=False)
                email = db.Column(db.String(120), unique=True, nullable=False)
                created_at = db.Column(db.DateTime, default=datetime.utcnow)

                def __repr__(self):
                    return f'<User {self.username}>'
        """).strip()

    def _generate_flask_auth(self) -> str:
        """Generate Flask authentication module."""
        return dedent("""
            from flask import Blueprint, request, jsonify
            from flask_login import login_user, logout_user, login_required
            from werkzeug.security import check_password_hash, generate_password_hash

            auth = Blueprint('auth', __name__)

            @auth.route('/login', methods=['POST'])
            def login():
                data = request.get_json()
                # Add authentication logic here
                return jsonify({'message': 'Login successful'})

            @auth.route('/logout')
            @login_required
            def logout():
                logout_user()
                return jsonify({'message': 'Logout successful'})
        """).strip()

    def _generate_fastapi_models(self) -> str:
        """Generate FastAPI SQLAlchemy models."""
        return dedent("""
            from sqlalchemy import Column, Integer, String, DateTime
            from sqlalchemy.ext.declarative import declarative_base
            from datetime import datetime

            Base = declarative_base()

            class User(Base):
                __tablename__ = "users"

                id = Column(Integer, primary_key=True, index=True)
                username = Column(String, unique=True, index=True)
                email = Column(String, unique=True, index=True)
                created_at = Column(DateTime, default=datetime.utcnow)
        """).strip()

    def _generate_cli_app(self, name: str) -> str:
        """Generate CLI application using Click."""
        return dedent(f"""
            import click

            @click.group()
            def cli():
                \"\"\"Command line interface for {name}.\"\"\"
                pass

            @cli.command()
            @click.option('--name', default='World', help='Name to greet.')
            def hello(name):
                \"\"\"Simple program that greets NAME.\"\"\"
                click.echo(f'Hello {{name}}!')

            @cli.command()
            def version():
                \"\"\"Show the version.\"\"\"
                click.echo('{name} version 1.0.0')

            if __name__ == '__main__':
                cli()
        """).strip()

    def _generate_pytest_template(self, name: str) -> str:
        """Generate pytest test template."""
        return dedent(f"""
            import pytest

            def test_{name.lower()}_example():
                \"\"\"Test example for {name}.\"\"\"
                assert True

            def test_{name.lower()}_with_fixture(sample_data):
                \"\"\"Test using fixture.\"\"\"
                assert sample_data is not None

            @pytest.fixture
            def sample_data():
                \"\"\"Sample test data.\"\"\"
                return {{"key": "value"}}

            class Test{name}:
                \"\"\"Test class for {name}.\"\"\"

                def test_method(self):
                    \"\"\"Test method.\"\"\"
                    assert True
        """).strip()


class TestGenerator(ast.NodeVisitor):
    """AST visitor for generating test cases."""

    def __init__(self, framework: str = "pytest"):
        self.framework = framework
        self.functions = []
        self.classes = []
        self.test_count = 0

    def visit_FunctionDef(self, node):
        if not node.name.startswith('_'):  # Skip private functions
            self.functions.append({
                'name': node.name,
                'args': [arg.arg for arg in node.args.args],
                'lineno': node.lineno
            })
        self.generic_visit(node)

    def visit_ClassDef(self, node):
        methods = []
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and not item.name.startswith('_'):
                methods.append(item.name)

        self.classes.append({
            'name': node.name,
            'methods': methods,
            'lineno': node.lineno
        })
        self.generic_visit(node)

    def generate_tests(self) -> str:
        """Generate test code based on discovered functions and classes."""
        test_lines = []

        if self.framework == "pytest":
            test_lines.append("import pytest")
            test_lines.append("")

            # Generate function tests
            for func in self.functions:
                test_name = f"test_{func['name']}"
                test_lines.append(f"def {test_name}():")
                test_lines.append(f'    """Test {func["name"]} function."""')
                test_lines.append("    # TODO: Implement test logic")
                test_lines.append("    assert True")
                test_lines.append("")
                self.test_count += 1

            # Generate class tests
            for cls in self.classes:
                test_lines.append(f"class Test{cls['name']}:")
                test_lines.append(f'    """Test {cls["name"]} class."""')
                test_lines.append("")

                for method in cls['methods']:
                    test_lines.append(f"    def test_{method}(self):")
                    test_lines.append(f'        """Test {method} method."""')
                    test_lines.append("        # TODO: Implement test logic")
                    test_lines.append("        assert True")
                    test_lines.append("")
                    self.test_count += 1

        return "\n".join(test_lines)

    def get_imports(self) -> List[str]:
        """Get required imports for tests."""
        imports = []
        if self.framework == "pytest":
            imports.append("import pytest")
        return imports
