"""
Code Optimization Plugin

Provides code optimization capabilities including performance improvements,
memory optimization, and algorithmic enhancements.
"""

import ast
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from textwrap import dedent

from agent_framework.core.types import (
    PluginInterface, PluginRequest, PluginResponse, PluginCapability
)


class CodeOptimizationPlugin(PluginInterface):
    """
    Plugin for optimizing code performance and efficiency.

    Provides capabilities for:
    - Performance optimization suggestions
    - Memory usage optimization
    - Algorithmic improvements
    - Code refactoring for efficiency
    - Bottleneck identification
    """

    PLUGIN_NAME = "code_optimization"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Optimizes code for performance and efficiency"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []

    def __init__(self):
        """Initialize the code optimization plugin."""
        self._is_initialized = False
        self._config = {}
        self._optimization_patterns = {}
        self._load_optimization_patterns()

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        start_time = time.time()

        try:
            capability = request.capability
            parameters = request.parameters

            if capability == "optimize_performance":
                result = await self._optimize_performance(parameters)
            elif capability == "optimize_memory":
                result = await self._optimize_memory(parameters)
            elif capability == "suggest_algorithms":
                result = await self._suggest_algorithms(parameters)
            elif capability == "identify_bottlenecks":
                result = await self._identify_bottlenecks(parameters)
            elif capability == "refactor_for_efficiency":
                result = await self._refactor_for_efficiency(parameters)
            elif capability == "analyze_complexity":
                result = await self._analyze_complexity(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "capability": capability,
                    "parameters": parameters
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return [
            PluginCapability(
                name="optimize_performance",
                description="Analyze and optimize code performance",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to optimize"},
                        "file_path": {"type": "string", "description": "Path to Python file"},
                        "target_metric": {"type": "string", "enum": ["speed", "memory", "both"], "default": "speed"}
                    },
                    "oneOf": [
                        {"required": ["code"]},
                        {"required": ["file_path"]}
                    ]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "optimizations": {"type": "array"},
                        "optimized_code": {"type": "string"},
                        "performance_gain": {"type": "number"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="optimize_memory",
                description="Optimize code for memory efficiency",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "memory_profile": {"type": "object", "description": "Optional memory profiling data"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "optimizations": {"type": "array"},
                        "memory_savings": {"type": "number"},
                        "recommendations": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="suggest_algorithms",
                description="Suggest better algorithms for common patterns",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "problem_type": {"type": "string", "enum": ["sorting", "searching", "graph", "dynamic_programming", "general"]}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "suggestions": {"type": "array"},
                        "complexity_improvements": {"type": "object"},
                        "alternative_code": {"type": "string"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="identify_bottlenecks",
                description="Identify performance bottlenecks in code",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "profile_data": {"type": "object", "description": "Optional profiling data"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "bottlenecks": {"type": "array"},
                        "hotspots": {"type": "array"},
                        "optimization_priority": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="refactor_for_efficiency",
                description="Refactor code for better efficiency",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "refactoring_type": {"type": "string", "enum": ["loops", "functions", "data_structures", "all"]}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "refactored_code": {"type": "string"},
                        "changes": {"type": "array"},
                        "efficiency_gain": {"type": "number"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="analyze_complexity",
                description="Analyze time and space complexity",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "function_name": {"type": "string", "description": "Specific function to analyze"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "time_complexity": {"type": "string"},
                        "space_complexity": {"type": "string"},
                        "analysis": {"type": "object"}
                    }
                },
                supported_languages=["python"]
            )
        ]

    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self._config.clear()
        self._optimization_patterns.clear()

    def _load_optimization_patterns(self) -> None:
        """Load optimization patterns and transformations."""
        self._optimization_patterns = {
            "list_comprehension": {
                "pattern": r"for .+ in .+:\s+.+\.append\(",
                "description": "Replace loop with list comprehension",
                "example": "[x for x in items if condition]"
            },
            "generator_expression": {
                "pattern": r"\[.+ for .+ in .+\]",
                "description": "Use generator expression for memory efficiency",
                "example": "(x for x in items if condition)"
            },
            "set_membership": {
                "pattern": r".+ in \[.+\]",
                "description": "Use set for O(1) membership testing",
                "example": "item in {set_items}"
            },
            "string_join": {
                "pattern": r".*\+.*\+.*",
                "description": "Use str.join() for string concatenation",
                "example": "''.join(string_list)"
            },
            "enumerate_usage": {
                "pattern": r"range\(len\(.+\)\)",
                "description": "Use enumerate() instead of range(len())",
                "example": "for i, item in enumerate(items)"
            }
        }

    async def _optimize_performance(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and optimize code performance."""
        code = await self._get_code(parameters)
        target_metric = parameters.get("target_metric", "speed")

        optimizations = []
        optimized_code = code
        performance_gain = 0.0

        try:
            tree = ast.parse(code)
            optimizer = PerformanceOptimizer(target_metric)
            optimizer.visit(tree)

            optimizations = optimizer.get_optimizations()
            optimized_code = optimizer.apply_optimizations(code)
            performance_gain = optimizer.estimate_performance_gain()

        except SyntaxError:
            optimizations.append({
                "type": "SyntaxError",
                "message": "Cannot optimize code with syntax errors",
                "priority": "high"
            })

        return {
            "optimizations": optimizations,
            "optimized_code": optimized_code,
            "performance_gain": performance_gain,
            "metadata": {
                "target_metric": target_metric,
                "optimization_count": len(optimizations)
            }
        }

    async def _optimize_memory(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize code for memory efficiency."""
        code = parameters.get("code", "")
        memory_profile = parameters.get("memory_profile", {})

        optimizations = []
        memory_savings = 0.0
        recommendations = []

        try:
            tree = ast.parse(code)
            memory_optimizer = MemoryOptimizer()
            memory_optimizer.visit(tree)

            optimizations = memory_optimizer.get_optimizations()
            memory_savings = memory_optimizer.estimate_memory_savings()
            recommendations = memory_optimizer.get_recommendations()

        except SyntaxError:
            optimizations.append({
                "type": "SyntaxError",
                "message": "Cannot optimize memory for code with syntax errors"
            })

        return {
            "optimizations": optimizations,
            "memory_savings": memory_savings,
            "recommendations": recommendations,
            "metadata": {
                "profile_data_available": bool(memory_profile),
                "optimization_count": len(optimizations)
            }
        }

    async def _suggest_algorithms(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest better algorithms for common patterns."""
        code = parameters.get("code", "")
        problem_type = parameters.get("problem_type", "general")

        suggestions = []
        complexity_improvements = {}
        alternative_code = ""

        try:
            tree = ast.parse(code)
            algorithm_analyzer = AlgorithmAnalyzer(problem_type)
            algorithm_analyzer.visit(tree)

            suggestions = algorithm_analyzer.get_suggestions()
            complexity_improvements = algorithm_analyzer.get_complexity_improvements()
            alternative_code = algorithm_analyzer.generate_alternative_code(code)

        except SyntaxError:
            suggestions.append({
                "type": "SyntaxError",
                "message": "Cannot analyze algorithms in code with syntax errors"
            })

        return {
            "suggestions": suggestions,
            "complexity_improvements": complexity_improvements,
            "alternative_code": alternative_code,
            "metadata": {
                "problem_type": problem_type,
                "suggestion_count": len(suggestions)
            }
        }

    async def _identify_bottlenecks(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Identify performance bottlenecks in code."""
        code = parameters.get("code", "")
        profile_data = parameters.get("profile_data", {})

        bottlenecks = []
        hotspots = []
        optimization_priority = []

        try:
            tree = ast.parse(code)
            bottleneck_analyzer = BottleneckAnalyzer(profile_data)
            bottleneck_analyzer.visit(tree)

            bottlenecks = bottleneck_analyzer.get_bottlenecks()
            hotspots = bottleneck_analyzer.get_hotspots()
            optimization_priority = bottleneck_analyzer.get_optimization_priority()

        except SyntaxError:
            bottlenecks.append({
                "type": "SyntaxError",
                "message": "Cannot identify bottlenecks in code with syntax errors",
                "severity": "high"
            })

        return {
            "bottlenecks": bottlenecks,
            "hotspots": hotspots,
            "optimization_priority": optimization_priority,
            "metadata": {
                "profile_data_available": bool(profile_data),
                "bottleneck_count": len(bottlenecks)
            }
        }

    async def _refactor_for_efficiency(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Refactor code for better efficiency."""
        code = parameters.get("code", "")
        refactoring_type = parameters.get("refactoring_type", "all")

        refactored_code = code
        changes = []
        efficiency_gain = 0.0

        try:
            tree = ast.parse(code)
            refactorer = EfficiencyRefactorer(refactoring_type)
            refactorer.visit(tree)

            refactored_code = refactorer.apply_refactoring(code)
            changes = refactorer.get_changes()
            efficiency_gain = refactorer.estimate_efficiency_gain()

        except SyntaxError:
            changes.append({
                "type": "SyntaxError",
                "message": "Cannot refactor code with syntax errors"
            })

        return {
            "refactored_code": refactored_code,
            "changes": changes,
            "efficiency_gain": efficiency_gain,
            "metadata": {
                "refactoring_type": refactoring_type,
                "change_count": len(changes)
            }
        }

    async def _analyze_complexity(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze time and space complexity."""
        code = parameters.get("code", "")
        function_name = parameters.get("function_name", "")

        time_complexity = "O(1)"
        space_complexity = "O(1)"
        analysis = {}

        try:
            tree = ast.parse(code)
            complexity_analyzer = ComplexityAnalyzer(function_name)
            complexity_analyzer.visit(tree)

            time_complexity = complexity_analyzer.get_time_complexity()
            space_complexity = complexity_analyzer.get_space_complexity()
            analysis = complexity_analyzer.get_detailed_analysis()

        except SyntaxError:
            analysis = {
                "error": "Cannot analyze complexity of code with syntax errors"
            }

        return {
            "time_complexity": time_complexity,
            "space_complexity": space_complexity,
            "analysis": analysis,
            "metadata": {
                "function_name": function_name,
                "analysis_complete": "error" not in analysis
            }
        }

    async def _get_code(self, parameters: Dict[str, Any]) -> str:
        """Get code from parameters (either direct code or file path)."""
        if "code" in parameters:
            return parameters["code"]
        elif "file_path" in parameters:
            file_path = Path(parameters["file_path"])
            if not file_path.exists():
                raise ValueError(f"File not found: {file_path}")
            return file_path.read_text(encoding='utf-8')
        else:
            raise ValueError("Either 'code' or 'file_path' parameter is required")


class PerformanceOptimizer(ast.NodeVisitor):
    """AST visitor for performance optimization."""

    def __init__(self, target_metric: str = "speed"):
        self.target_metric = target_metric
        self.optimizations = []
        self.performance_gain = 0.0

    def visit_For(self, node):
        # Check for list append in loop
        for child in ast.walk(node):
            if isinstance(child, ast.Call) and isinstance(child.func, ast.Attribute):
                if child.func.attr == "append":
                    self.optimizations.append({
                        "type": "ListComprehension",
                        "message": "Replace loop with list comprehension",
                        "line": node.lineno,
                        "priority": "medium",
                        "estimated_gain": 20
                    })
                    self.performance_gain += 20
                    break

        # Check for range(len()) pattern
        if isinstance(node.iter, ast.Call) and isinstance(node.iter.func, ast.Name):
            if node.iter.func.id == "range" and len(node.iter.args) == 1:
                if isinstance(node.iter.args[0], ast.Call) and isinstance(node.iter.args[0].func, ast.Name):
                    if node.iter.args[0].func.id == "len":
                        self.optimizations.append({
                            "type": "Enumerate",
                            "message": "Use enumerate() instead of range(len())",
                            "line": node.lineno,
                            "priority": "low",
                            "estimated_gain": 5
                        })
                        self.performance_gain += 5

        self.generic_visit(node)

    def visit_Compare(self, node):
        # Check for membership testing in list
        if isinstance(node.ops[0], ast.In) and isinstance(node.comparators[0], ast.List):
            self.optimizations.append({
                "type": "SetMembership",
                "message": "Use set for O(1) membership testing instead of list",
                "line": node.lineno,
                "priority": "high",
                "estimated_gain": 50
            })
            self.performance_gain += 50

        self.generic_visit(node)

    def visit_BinOp(self, node):
        # Check for string concatenation
        if isinstance(node.op, ast.Add) and self._is_string_operation(node):
            self.optimizations.append({
                "type": "StringJoin",
                "message": "Use str.join() for string concatenation",
                "line": node.lineno,
                "priority": "medium",
                "estimated_gain": 30
            })
            self.performance_gain += 30

        self.generic_visit(node)

    def _is_string_operation(self, node) -> bool:
        """Check if binary operation involves strings."""
        # Simplified check - in practice, would need type inference
        return True  # Assume string operation for demonstration

    def get_optimizations(self) -> List[Dict[str, Any]]:
        """Get list of optimization suggestions."""
        return self.optimizations

    def estimate_performance_gain(self) -> float:
        """Estimate overall performance gain percentage."""
        return min(self.performance_gain, 100)  # Cap at 100%

    def apply_optimizations(self, code: str) -> str:
        """Apply optimizations to code (simplified implementation)."""
        optimized = code

        # Simple pattern replacements
        for opt in self.optimizations:
            if opt["type"] == "ListComprehension":
                # This would need more sophisticated AST transformation
                optimized = re.sub(
                    r"for .+ in .+:\s+.+\.append\(.+\)",
                    "# TODO: Replace with list comprehension",
                    optimized
                )
            elif opt["type"] == "SetMembership":
                optimized = re.sub(
                    r"(.+) in \[(.+)\]",
                    r"\1 in {\2}",
                    optimized
                )

        return optimized


class MemoryOptimizer(ast.NodeVisitor):
    """AST visitor for memory optimization."""

    def __init__(self):
        self.optimizations = []
        self.memory_savings = 0.0

    def visit_ListComp(self, node):
        # Suggest generator expression for large datasets
        self.optimizations.append({
            "type": "GeneratorExpression",
            "message": "Consider using generator expression for memory efficiency",
            "line": node.lineno,
            "memory_saving": 30
        })
        self.memory_savings += 30
        self.generic_visit(node)

    def visit_Call(self, node):
        # Check for list() on generator
        if isinstance(node.func, ast.Name) and node.func.id == "list":
            if len(node.args) == 1 and isinstance(node.args[0], ast.GeneratorExp):
                self.optimizations.append({
                    "type": "AvoidListConversion",
                    "message": "Avoid converting generator to list if not necessary",
                    "line": node.lineno,
                    "memory_saving": 40
                })
                self.memory_savings += 40

        self.generic_visit(node)

    def get_optimizations(self) -> List[Dict[str, Any]]:
        """Get memory optimization suggestions."""
        return self.optimizations

    def estimate_memory_savings(self) -> float:
        """Estimate memory savings percentage."""
        return min(self.memory_savings, 100)

    def get_recommendations(self) -> List[str]:
        """Get general memory optimization recommendations."""
        recommendations = []

        if any(opt["type"] == "GeneratorExpression" for opt in self.optimizations):
            recommendations.append("Use generators for large datasets to reduce memory usage")

        if any(opt["type"] == "AvoidListConversion" for opt in self.optimizations):
            recommendations.append("Avoid unnecessary list conversions")

        recommendations.extend([
            "Use __slots__ in classes to reduce memory overhead",
            "Consider using array.array for numeric data",
            "Use itertools for memory-efficient iteration"
        ])

        return recommendations


class AlgorithmAnalyzer(ast.NodeVisitor):
    """AST visitor for algorithm analysis and suggestions."""

    def __init__(self, problem_type: str = "general"):
        self.problem_type = problem_type
        self.suggestions = []
        self.complexity_improvements = {}

    def visit_For(self, node):
        # Detect sorting patterns
        for child in ast.walk(node):
            if isinstance(child, ast.Compare) and len(child.ops) == 1:
                if isinstance(child.ops[0], (ast.Lt, ast.Gt)):
                    self.suggestions.append({
                        "type": "SortingAlgorithm",
                        "message": "Consider using built-in sorted() or list.sort()",
                        "line": node.lineno,
                        "improvement": "O(n log n) vs O(n²)"
                    })
                    self.complexity_improvements["sorting"] = {
                        "current": "O(n²)",
                        "improved": "O(n log n)"
                    }
                    break

        self.generic_visit(node)

    def visit_While(self, node):
        # Detect linear search patterns
        self.suggestions.append({
            "type": "SearchAlgorithm",
            "message": "Consider using binary search for sorted data",
            "line": node.lineno,
            "improvement": "O(log n) vs O(n)"
        })
        self.complexity_improvements["search"] = {
            "current": "O(n)",
            "improved": "O(log n)"
        }

        self.generic_visit(node)

    def get_suggestions(self) -> List[Dict[str, Any]]:
        """Get algorithm improvement suggestions."""
        return self.suggestions

    def get_complexity_improvements(self) -> Dict[str, Any]:
        """Get complexity improvement information."""
        return self.complexity_improvements

    def generate_alternative_code(self, original_code: str) -> str:
        """Generate alternative code with better algorithms."""
        # Simplified implementation
        if "sorting" in self.complexity_improvements:
            return "# Use sorted() or list.sort() for efficient sorting\n" + original_code
        elif "search" in self.complexity_improvements:
            return "# Use bisect module for binary search\n" + original_code
        else:
            return original_code


class BottleneckAnalyzer(ast.NodeVisitor):
    """AST visitor for identifying performance bottlenecks."""

    def __init__(self, profile_data: Dict[str, Any] = None):
        self.profile_data = profile_data or {}
        self.bottlenecks = []
        self.hotspots = []

    def visit_For(self, node):
        # Nested loops are potential bottlenecks
        nested_loops = 0
        for child in ast.walk(node):
            if isinstance(child, (ast.For, ast.While)) and child != node:
                nested_loops += 1

        if nested_loops > 0:
            self.bottlenecks.append({
                "type": "NestedLoop",
                "message": f"Nested loop with {nested_loops} inner loops",
                "line": node.lineno,
                "severity": "high" if nested_loops > 1 else "medium"
            })

        self.generic_visit(node)

    def visit_Call(self, node):
        # Function calls in loops can be bottlenecks
        if self._is_in_loop(node):
            if isinstance(node.func, ast.Attribute):
                self.bottlenecks.append({
                    "type": "FunctionCallInLoop",
                    "message": f"Function call '{node.func.attr}' inside loop",
                    "line": node.lineno,
                    "severity": "medium"
                })

        self.generic_visit(node)

    def _is_in_loop(self, node) -> bool:
        """Check if node is inside a loop (simplified)."""
        # In practice, would need to track AST hierarchy
        return True  # Simplified for demonstration

    def get_bottlenecks(self) -> List[Dict[str, Any]]:
        """Get identified bottlenecks."""
        return self.bottlenecks

    def get_hotspots(self) -> List[Dict[str, Any]]:
        """Get performance hotspots."""
        # Would use profile data if available
        return self.hotspots

    def get_optimization_priority(self) -> List[Dict[str, Any]]:
        """Get optimization priority list."""
        priority = []

        # Sort bottlenecks by severity
        high_priority = [b for b in self.bottlenecks if b.get("severity") == "high"]
        medium_priority = [b for b in self.bottlenecks if b.get("severity") == "medium"]

        priority.extend(high_priority)
        priority.extend(medium_priority)

        return priority


class EfficiencyRefactorer(ast.NodeVisitor):
    """AST visitor for efficiency refactoring."""

    def __init__(self, refactoring_type: str = "all"):
        self.refactoring_type = refactoring_type
        self.changes = []
        self.efficiency_gain = 0.0

    def visit_For(self, node):
        if self.refactoring_type in ["loops", "all"]:
            # Check for list comprehension opportunities
            for child in ast.walk(node):
                if isinstance(child, ast.Call) and isinstance(child.func, ast.Attribute):
                    if child.func.attr == "append":
                        self.changes.append({
                            "type": "LoopToListComp",
                            "message": "Convert loop to list comprehension",
                            "line": node.lineno,
                            "efficiency_gain": 15
                        })
                        self.efficiency_gain += 15
                        break

        self.generic_visit(node)

    def visit_FunctionDef(self, node):
        if self.refactoring_type in ["functions", "all"]:
            # Check function length
            if len(node.body) > 20:
                self.changes.append({
                    "type": "FunctionDecomposition",
                    "message": f"Consider breaking down large function '{node.name}'",
                    "line": node.lineno,
                    "efficiency_gain": 10
                })
                self.efficiency_gain += 10

        self.generic_visit(node)

    def visit_List(self, node):
        if self.refactoring_type in ["data_structures", "all"]:
            # Suggest set for membership testing
            self.changes.append({
                "type": "DataStructureOptimization",
                "message": "Consider using set for frequent membership testing",
                "line": node.lineno,
                "efficiency_gain": 25
            })
            self.efficiency_gain += 25

        self.generic_visit(node)

    def get_changes(self) -> List[Dict[str, Any]]:
        """Get refactoring changes."""
        return self.changes

    def estimate_efficiency_gain(self) -> float:
        """Estimate efficiency gain percentage."""
        return min(self.efficiency_gain, 100)

    def apply_refactoring(self, code: str) -> str:
        """Apply refactoring changes (simplified)."""
        refactored = code

        # Add comments for suggested changes
        for change in self.changes:
            if change["type"] == "LoopToListComp":
                refactored = f"# TODO: {change['message']}\n" + refactored

        return refactored


class ComplexityAnalyzer(ast.NodeVisitor):
    """AST visitor for complexity analysis."""

    def __init__(self, function_name: str = ""):
        self.function_name = function_name
        self.time_complexity = "O(1)"
        self.space_complexity = "O(1)"
        self.analysis = {}
        self.loop_depth = 0
        self.max_loop_depth = 0
        self.recursive_calls = 0

    def visit_For(self, node):
        self.loop_depth += 1
        self.max_loop_depth = max(self.max_loop_depth, self.loop_depth)
        self.generic_visit(node)
        self.loop_depth -= 1

    def visit_While(self, node):
        self.loop_depth += 1
        self.max_loop_depth = max(self.max_loop_depth, self.loop_depth)
        self.generic_visit(node)
        self.loop_depth -= 1

    def visit_Call(self, node):
        # Check for recursive calls
        if isinstance(node.func, ast.Name) and node.func.id == self.function_name:
            self.recursive_calls += 1
        self.generic_visit(node)

    def get_time_complexity(self) -> str:
        """Calculate time complexity based on analysis."""
        if self.recursive_calls > 0:
            return "O(2^n)"  # Simplified - depends on recursion type
        elif self.max_loop_depth == 0:
            return "O(1)"
        elif self.max_loop_depth == 1:
            return "O(n)"
        elif self.max_loop_depth == 2:
            return "O(n²)"
        elif self.max_loop_depth == 3:
            return "O(n³)"
        else:
            return f"O(n^{self.max_loop_depth})"

    def get_space_complexity(self) -> str:
        """Calculate space complexity based on analysis."""
        if self.recursive_calls > 0:
            return "O(n)"  # Stack space for recursion
        else:
            return "O(1)"  # Simplified analysis

    def get_detailed_analysis(self) -> Dict[str, Any]:
        """Get detailed complexity analysis."""
        return {
            "max_loop_depth": self.max_loop_depth,
            "recursive_calls": self.recursive_calls,
            "time_complexity": self.get_time_complexity(),
            "space_complexity": self.get_space_complexity(),
            "analysis_notes": [
                f"Maximum loop nesting depth: {self.max_loop_depth}",
                f"Recursive calls detected: {self.recursive_calls}",
                "Analysis based on static code structure"
            ]
        }
