"""
Code Analysis Plugin

Provides code analysis capabilities including complexity analysis,
code quality metrics, and pattern detection.
"""

import ast
import time
from pathlib import Path
from typing import Any, Dict, List

from agent_framework.core.types import (
    PluginInterface, PluginRequest, PluginResponse, PluginCapability
)


class CodeAnalysisPlugin(PluginInterface):
    """
    Plugin for analyzing code quality, complexity, and patterns.

    Provides capabilities for:
    - Cyclomatic complexity analysis
    - Code quality metrics
    - Pattern detection
    - Dependency analysis
    """

    PLUGIN_NAME = "code_analysis"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Analyzes code quality, complexity, and patterns"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []

    def __init__(self):
        """Initialize the code analysis plugin."""
        self._is_initialized = False
        self._config = {}

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        start_time = time.time()

        try:
            capability = request.capability
            parameters = request.parameters

            if capability == "analyze_complexity":
                result = await self._analyze_complexity(parameters)
            elif capability == "analyze_quality":
                result = await self._analyze_quality(parameters)
            elif capability == "detect_patterns":
                result = await self._detect_patterns(parameters)
            elif capability == "analyze_dependencies":
                result = await self._analyze_dependencies(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "capability": capability,
                    "parameters": parameters
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return [
            PluginCapability(
                name="analyze_complexity",
                description="Analyze cyclomatic complexity of code",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to analyze"},
                        "file_path": {"type": "string", "description": "Path to Python file"}
                    },
                    "oneOf": [
                        {"required": ["code"]},
                        {"required": ["file_path"]}
                    ]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "complexity": {"type": "number"},
                        "functions": {"type": "array"},
                        "classes": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="analyze_quality",
                description="Analyze code quality metrics",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "file_path": {"type": "string"}
                    }
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "lines_of_code": {"type": "number"},
                        "maintainability_index": {"type": "number"},
                        "issues": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="detect_patterns",
                description="Detect code patterns and anti-patterns",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "patterns": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "patterns_found": {"type": "array"},
                        "anti_patterns": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="analyze_dependencies",
                description="Analyze code dependencies and imports",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "file_path": {"type": "string"}
                    }
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "imports": {"type": "array"},
                        "dependencies": {"type": "array"},
                        "circular_dependencies": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            )
        ]

    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self._config.clear()

    async def _analyze_complexity(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze cyclomatic complexity of code."""
        code = await self._get_code(parameters)

        try:
            tree = ast.parse(code)
            complexity_analyzer = ComplexityAnalyzer()
            complexity_analyzer.visit(tree)

            return {
                "overall_complexity": complexity_analyzer.total_complexity,
                "functions": complexity_analyzer.functions,
                "classes": complexity_analyzer.classes,
                "average_complexity": complexity_analyzer.average_complexity
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _analyze_quality(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code quality metrics."""
        code = await self._get_code(parameters)

        lines = code.split('\n')
        total_lines = len(lines)
        code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        comment_lines = len([line for line in lines if line.strip().startswith('#')])

        # Simple maintainability index calculation
        maintainability_index = max(0, min(100, 171 - 5.2 * (total_lines / 1000) - 0.23 * 10 - 16.2 * (comment_lines / total_lines if total_lines > 0 else 0)))

        issues = []
        if code_lines > 1000:
            issues.append("File is very large (>1000 lines)")
        if comment_lines / total_lines < 0.1 if total_lines > 0 else True:
            issues.append("Low comment ratio")

        return {
            "lines_of_code": code_lines,
            "total_lines": total_lines,
            "comment_lines": comment_lines,
            "maintainability_index": maintainability_index,
            "issues": issues
        }

    async def _detect_patterns(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Detect code patterns and anti-patterns."""
        code = parameters.get("code", "")
        patterns_to_find = parameters.get("patterns", ["singleton", "factory", "observer"])

        patterns_found = []
        anti_patterns = []

        # Simple pattern detection
        if "singleton" in patterns_to_find and "class" in code and "__new__" in code:
            patterns_found.append("Singleton pattern detected")

        if "factory" in patterns_to_find and "def create" in code:
            patterns_found.append("Factory pattern detected")

        # Anti-pattern detection
        if "global " in code:
            anti_patterns.append("Global variables detected")

        if code.count("if") > 10:
            anti_patterns.append("Too many conditional statements")

        return {
            "patterns_found": patterns_found,
            "anti_patterns": anti_patterns
        }

    async def _analyze_dependencies(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code dependencies and imports."""
        code = await self._get_code(parameters)

        try:
            tree = ast.parse(code)
            dependency_analyzer = DependencyAnalyzer()
            dependency_analyzer.visit(tree)

            return {
                "imports": dependency_analyzer.imports,
                "from_imports": dependency_analyzer.from_imports,
                "dependencies": dependency_analyzer.get_dependencies(),
                "circular_dependencies": []  # Would need more complex analysis
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _get_code(self, parameters: Dict[str, Any]) -> str:
        """Get code from parameters (either direct code or file path)."""
        if "code" in parameters:
            return parameters["code"]
        elif "file_path" in parameters:
            file_path = Path(parameters["file_path"])
            if not file_path.exists():
                raise ValueError(f"File not found: {file_path}")
            return file_path.read_text(encoding='utf-8')
        else:
            raise ValueError("Either 'code' or 'file_path' parameter is required")


class ComplexityAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing cyclomatic complexity."""

    def __init__(self):
        self.total_complexity = 1  # Base complexity
        self.functions = []
        self.classes = []
        self.current_function = None
        self.current_class = None

    def visit_FunctionDef(self, node):
        function_complexity = 1  # Base complexity for function
        self.current_function = node.name

        # Count decision points
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                function_complexity += 1
            elif isinstance(child, ast.BoolOp):
                function_complexity += len(child.values) - 1

        self.functions.append({
            "name": node.name,
            "complexity": function_complexity,
            "line": node.lineno
        })

        self.total_complexity += function_complexity - 1
        self.generic_visit(node)

    def visit_ClassDef(self, node):
        self.current_class = node.name
        self.classes.append({
            "name": node.name,
            "line": node.lineno,
            "methods": []
        })
        self.generic_visit(node)

    @property
    def average_complexity(self) -> float:
        if not self.functions:
            return 0.0
        return sum(f["complexity"] for f in self.functions) / len(self.functions)


class DependencyAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing dependencies."""

    def __init__(self):
        self.imports = []
        self.from_imports = []

    def visit_Import(self, node):
        for alias in node.names:
            self.imports.append(alias.name)

    def visit_ImportFrom(self, node):
        module = node.module or ""
        for alias in node.names:
            self.from_imports.append(f"{module}.{alias.name}")

    def get_dependencies(self) -> List[str]:
        """Get unique list of all dependencies."""
        all_deps = self.imports + [imp.split('.')[0] for imp in self.from_imports]
        return list(set(all_deps))