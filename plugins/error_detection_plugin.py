"""
Error Detection Plugin

Provides error detection and debugging assistance including syntax checking,
runtime error prediction, and debugging suggestions.
"""

import ast
import re
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from textwrap import dedent

from agent_framework.core.types import (
    PluginInterface, PluginRequest, PluginResponse, PluginCapability
)


class ErrorDetectionPlugin(PluginInterface):
    """
    Plugin for detecting errors and providing debugging assistance.

    Provides capabilities for:
    - Syntax error detection and fixing
    - Runtime error prediction
    - Code smell detection
    - Debugging suggestions
    - Performance issue identification
    """

    PLUGIN_NAME = "error_detection"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Detects errors and provides debugging assistance"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []

    def __init__(self):
        """Initialize the error detection plugin."""
        self._is_initialized = False
        self._config = {}
        self._error_patterns = {}
        self._load_error_patterns()

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        start_time = time.time()

        try:
            capability = request.capability
            parameters = request.parameters

            if capability == "check_syntax":
                result = await self._check_syntax(parameters)
            elif capability == "detect_runtime_errors":
                result = await self._detect_runtime_errors(parameters)
            elif capability == "detect_code_smells":
                result = await self._detect_code_smells(parameters)
            elif capability == "suggest_fixes":
                result = await self._suggest_fixes(parameters)
            elif capability == "analyze_performance":
                result = await self._analyze_performance(parameters)
            elif capability == "debug_traceback":
                result = await self._debug_traceback(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "capability": capability,
                    "parameters": parameters
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return [
            PluginCapability(
                name="check_syntax",
                description="Check code for syntax errors",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to check"},
                        "file_path": {"type": "string", "description": "Path to Python file"}
                    },
                    "oneOf": [
                        {"required": ["code"]},
                        {"required": ["file_path"]}
                    ]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "is_valid": {"type": "boolean"},
                        "errors": {"type": "array"},
                        "suggestions": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="detect_runtime_errors",
                description="Predict potential runtime errors",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "context": {"type": "object", "description": "Runtime context"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "potential_errors": {"type": "array"},
                        "risk_level": {"type": "string"},
                        "recommendations": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="detect_code_smells",
                description="Detect code smells and anti-patterns",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "severity_level": {"type": "string", "enum": ["low", "medium", "high"], "default": "medium"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "smells": {"type": "array"},
                        "severity_counts": {"type": "object"},
                        "refactoring_suggestions": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="suggest_fixes",
                description="Suggest fixes for detected errors",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "error_message": {"type": "string"},
                        "error_type": {"type": "string"}
                    },
                    "required": ["code", "error_message"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "fixes": {"type": "array"},
                        "confidence": {"type": "number"},
                        "explanation": {"type": "string"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="analyze_performance",
                description="Analyze code for performance issues",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "profile_data": {"type": "object", "description": "Optional profiling data"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "issues": {"type": "array"},
                        "optimizations": {"type": "array"},
                        "complexity_score": {"type": "number"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="debug_traceback",
                description="Analyze and explain error tracebacks",
                input_schema={
                    "type": "object",
                    "properties": {
                        "traceback": {"type": "string", "description": "Error traceback"},
                        "code": {"type": "string", "description": "Related code"}
                    },
                    "required": ["traceback"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "explanation": {"type": "string"},
                        "root_cause": {"type": "string"},
                        "suggested_fixes": {"type": "array"}
                    }
                },
                supported_languages=["python"]
            )
        ]

    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self._config.clear()
        self._error_patterns.clear()

    def _load_error_patterns(self) -> None:
        """Load common error patterns and their fixes."""
        self._error_patterns = {
            "NameError": {
                "pattern": r"name '(\w+)' is not defined",
                "suggestions": [
                    "Check if the variable is defined before use",
                    "Check for typos in variable name",
                    "Import the module if it's an external function"
                ]
            },
            "AttributeError": {
                "pattern": r"'(\w+)' object has no attribute '(\w+)'",
                "suggestions": [
                    "Check if the attribute exists for this object type",
                    "Check for typos in attribute name",
                    "Verify object initialization"
                ]
            },
            "TypeError": {
                "pattern": r"unsupported operand type\(s\) for (.+): '(\w+)' and '(\w+)'",
                "suggestions": [
                    "Check data types of operands",
                    "Convert types if necessary",
                    "Use appropriate operators for the data types"
                ]
            },
            "IndexError": {
                "pattern": r"list index out of range",
                "suggestions": [
                    "Check list length before accessing index",
                    "Use try-except for safe access",
                    "Verify loop bounds"
                ]
            },
            "KeyError": {
                "pattern": r"'(\w+)'",
                "suggestions": [
                    "Check if key exists in dictionary",
                    "Use dict.get() for safe access",
                    "Verify dictionary initialization"
                ]
            }
        }

    async def _check_syntax(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Check code for syntax errors."""
        code = await self._get_code(parameters)

        errors = []
        suggestions = []
        is_valid = True

        try:
            ast.parse(code)
        except SyntaxError as e:
            is_valid = False
            error_info = {
                "type": "SyntaxError",
                "message": str(e),
                "line": e.lineno,
                "column": e.offset,
                "text": e.text.strip() if e.text else ""
            }
            errors.append(error_info)

            # Generate suggestions based on common syntax errors
            suggestions.extend(self._get_syntax_suggestions(e))

        return {
            "is_valid": is_valid,
            "errors": errors,
            "suggestions": suggestions,
            "metadata": {
                "lines_checked": len(code.split('\n')),
                "error_count": len(errors)
            }
        }

    async def _detect_runtime_errors(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Predict potential runtime errors."""
        code = parameters.get("code", "")
        context = parameters.get("context", {})

        potential_errors = []
        risk_level = "low"
        recommendations = []

        try:
            tree = ast.parse(code)
            error_detector = RuntimeErrorDetector()
            error_detector.visit(tree)

            potential_errors = error_detector.potential_errors
            risk_level = error_detector.calculate_risk_level()
            recommendations = error_detector.get_recommendations()

        except SyntaxError:
            potential_errors.append({
                "type": "SyntaxError",
                "message": "Code contains syntax errors",
                "severity": "high"
            })
            risk_level = "high"

        return {
            "potential_errors": potential_errors,
            "risk_level": risk_level,
            "recommendations": recommendations,
            "metadata": {
                "analysis_context": context,
                "error_count": len(potential_errors)
            }
        }

    async def _detect_code_smells(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Detect code smells and anti-patterns."""
        code = parameters.get("code", "")
        severity_level = parameters.get("severity_level", "medium")

        smells = []
        severity_counts = {"low": 0, "medium": 0, "high": 0}
        refactoring_suggestions = []

        try:
            tree = ast.parse(code)
            smell_detector = CodeSmellDetector(severity_level)
            smell_detector.visit(tree)

            smells = smell_detector.smells
            severity_counts = smell_detector.severity_counts
            refactoring_suggestions = smell_detector.get_refactoring_suggestions()

        except SyntaxError:
            smells.append({
                "type": "SyntaxError",
                "message": "Code contains syntax errors",
                "severity": "high",
                "line": 0
            })
            severity_counts["high"] += 1

        return {
            "smells": smells,
            "severity_counts": severity_counts,
            "refactoring_suggestions": refactoring_suggestions,
            "metadata": {
                "total_smells": len(smells),
                "severity_filter": severity_level
            }
        }

    async def _suggest_fixes(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest fixes for detected errors."""
        code = parameters.get("code", "")
        error_message = parameters.get("error_message", "")
        error_type = parameters.get("error_type", "")

        fixes = []
        confidence = 0.0
        explanation = ""

        # Match error patterns and suggest fixes
        for err_type, pattern_info in self._error_patterns.items():
            if err_type in error_message or err_type == error_type:
                fixes.extend(pattern_info["suggestions"])
                confidence = 0.8
                explanation = f"Common fixes for {err_type}"
                break

        # Additional context-specific suggestions
        if "NameError" in error_message:
            # Extract variable name from error
            match = re.search(r"name '(\w+)' is not defined", error_message)
            if match:
                var_name = match.group(1)
                fixes.append(f"Define {var_name} before using it")
                fixes.append(f"Check if {var_name} should be imported")

        if not fixes:
            fixes = ["Review the error message and check the documentation"]
            confidence = 0.3
            explanation = "Generic suggestion - specific fix pattern not recognized"

        return {
            "fixes": fixes,
            "confidence": confidence,
            "explanation": explanation,
            "metadata": {
                "error_type": error_type,
                "error_message": error_message
            }
        }

    async def _analyze_performance(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code for performance issues."""
        code = parameters.get("code", "")
        profile_data = parameters.get("profile_data", {})

        issues = []
        optimizations = []
        complexity_score = 0

        try:
            tree = ast.parse(code)
            perf_analyzer = PerformanceAnalyzer()
            perf_analyzer.visit(tree)

            issues = perf_analyzer.issues
            optimizations = perf_analyzer.optimizations
            complexity_score = perf_analyzer.complexity_score

        except SyntaxError:
            issues.append({
                "type": "SyntaxError",
                "message": "Cannot analyze performance due to syntax errors",
                "severity": "high"
            })

        return {
            "issues": issues,
            "optimizations": optimizations,
            "complexity_score": complexity_score,
            "metadata": {
                "profile_data_provided": bool(profile_data),
                "issue_count": len(issues)
            }
        }

    async def _debug_traceback(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and explain error tracebacks."""
        traceback_text = parameters.get("traceback", "")
        code = parameters.get("code", "")

        explanation = ""
        root_cause = ""
        suggested_fixes = []

        # Parse traceback to extract error information
        lines = traceback_text.strip().split('\n')
        error_line = lines[-1] if lines else ""

        # Extract error type and message
        if ':' in error_line:
            error_type, error_message = error_line.split(':', 1)
            error_type = error_type.strip()
            error_message = error_message.strip()
        else:
            error_type = error_line
            error_message = ""

        # Generate explanation
        explanation = f"This is a {error_type} which occurs when {self._get_error_explanation(error_type)}"

        # Identify root cause
        root_cause = self._identify_root_cause(traceback_text, error_type)

        # Get suggested fixes
        if error_type in self._error_patterns:
            suggested_fixes = self._error_patterns[error_type]["suggestions"]
        else:
            suggested_fixes = ["Check the error message and review the code at the indicated line"]

        return {
            "explanation": explanation,
            "root_cause": root_cause,
            "suggested_fixes": suggested_fixes,
            "metadata": {
                "error_type": error_type,
                "error_message": error_message,
                "traceback_lines": len(lines)
            }
        }

    async def _get_code(self, parameters: Dict[str, Any]) -> str:
        """Get code from parameters (either direct code or file path)."""
        if "code" in parameters:
            return parameters["code"]
        elif "file_path" in parameters:
            file_path = Path(parameters["file_path"])
            if not file_path.exists():
                raise ValueError(f"File not found: {file_path}")
            return file_path.read_text(encoding='utf-8')
        else:
            raise ValueError("Either 'code' or 'file_path' parameter is required")

    def _get_syntax_suggestions(self, syntax_error: SyntaxError) -> List[str]:
        """Get suggestions for fixing syntax errors."""
        suggestions = []
        error_msg = str(syntax_error).lower()

        if "invalid syntax" in error_msg:
            suggestions.append("Check for missing colons, parentheses, or brackets")
            suggestions.append("Verify proper indentation")

        if "unexpected eof" in error_msg:
            suggestions.append("Check for unclosed parentheses, brackets, or quotes")

        if "indentation" in error_msg:
            suggestions.append("Use consistent indentation (spaces or tabs, not both)")
            suggestions.append("Check for missing or extra indentation")

        if not suggestions:
            suggestions.append("Review the syntax at the indicated line")

        return suggestions

    def _get_error_explanation(self, error_type: str) -> str:
        """Get explanation for common error types."""
        explanations = {
            "NameError": "a variable or function name is used but not defined",
            "TypeError": "an operation is performed on an inappropriate type",
            "ValueError": "a function receives an argument of correct type but inappropriate value",
            "AttributeError": "an attribute reference or assignment fails",
            "IndexError": "a sequence subscript is out of range",
            "KeyError": "a dictionary key is not found",
            "ImportError": "an import statement fails to find the module",
            "SyntaxError": "the parser encounters a syntax error",
            "IndentationError": "there is incorrect indentation",
            "ZeroDivisionError": "division or modulo by zero is attempted"
        }
        return explanations.get(error_type, "an error occurs during code execution")

    def _identify_root_cause(self, traceback_text: str, error_type: str) -> str:
        """Identify the root cause of an error from traceback."""
        lines = traceback_text.strip().split('\n')

        # Look for file and line information
        for line in lines:
            if 'File "' in line and 'line' in line:
                # Extract file and line number
                match = re.search(r'File "([^"]+)", line (\d+)', line)
                if match:
                    file_path, line_num = match.groups()
                    return f"Error occurred in {file_path} at line {line_num}"

        return f"Root cause: {error_type} in the code execution"


class RuntimeErrorDetector(ast.NodeVisitor):
    """AST visitor for detecting potential runtime errors."""

    def __init__(self):
        self.potential_errors = []
        self.risk_factors = []

    def visit_Name(self, node):
        # Check for potential NameError
        if isinstance(node.ctx, ast.Load):
            self.risk_factors.append(f"Variable '{node.id}' used - ensure it's defined")
        self.generic_visit(node)

    def visit_Subscript(self, node):
        # Check for potential IndexError/KeyError
        self.potential_errors.append({
            "type": "IndexError/KeyError",
            "message": "Subscript access without bounds checking",
            "line": node.lineno,
            "severity": "medium"
        })
        self.generic_visit(node)

    def visit_BinOp(self, node):
        # Check for potential ZeroDivisionError
        if isinstance(node.op, (ast.Div, ast.FloorDiv, ast.Mod)):
            self.potential_errors.append({
                "type": "ZeroDivisionError",
                "message": "Division operation without zero check",
                "line": node.lineno,
                "severity": "medium"
            })
        self.generic_visit(node)

    def visit_Call(self, node):
        # Check for potential AttributeError
        if isinstance(node.func, ast.Attribute):
            self.potential_errors.append({
                "type": "AttributeError",
                "message": "Method call without object validation",
                "line": node.lineno,
                "severity": "low"
            })
        self.generic_visit(node)

    def calculate_risk_level(self) -> str:
        """Calculate overall risk level based on detected issues."""
        high_risk = sum(1 for err in self.potential_errors if err["severity"] == "high")
        medium_risk = sum(1 for err in self.potential_errors if err["severity"] == "medium")

        if high_risk > 0:
            return "high"
        elif medium_risk > 2:
            return "high"
        elif medium_risk > 0:
            return "medium"
        else:
            return "low"

    def get_recommendations(self) -> List[str]:
        """Get recommendations based on detected risks."""
        recommendations = []

        if any("IndexError" in err["type"] for err in self.potential_errors):
            recommendations.append("Add bounds checking before array/list access")

        if any("ZeroDivisionError" in err["type"] for err in self.potential_errors):
            recommendations.append("Add zero checks before division operations")

        if any("AttributeError" in err["type"] for err in self.potential_errors):
            recommendations.append("Validate objects before calling methods")

        if not recommendations:
            recommendations.append("Code appears to have low runtime error risk")

        return recommendations


class CodeSmellDetector(ast.NodeVisitor):
    """AST visitor for detecting code smells."""

    def __init__(self, severity_level: str = "medium"):
        self.severity_level = severity_level
        self.smells = []
        self.severity_counts = {"low": 0, "medium": 0, "high": 0}
        self.function_lengths = []
        self.class_sizes = []

    def visit_FunctionDef(self, node):
        # Check function length
        func_length = len(node.body)
        self.function_lengths.append(func_length)

        if func_length > 50:
            self._add_smell("LongFunction", f"Function '{node.name}' is too long ({func_length} statements)",
                          node.lineno, "high")
        elif func_length > 20:
            self._add_smell("LongFunction", f"Function '{node.name}' is moderately long ({func_length} statements)",
                          node.lineno, "medium")

        # Check parameter count
        param_count = len(node.args.args)
        if param_count > 7:
            self._add_smell("TooManyParameters", f"Function '{node.name}' has too many parameters ({param_count})",
                          node.lineno, "high")
        elif param_count > 5:
            self._add_smell("TooManyParameters", f"Function '{node.name}' has many parameters ({param_count})",
                          node.lineno, "medium")

        self.generic_visit(node)

    def visit_ClassDef(self, node):
        # Check class size
        class_size = len(node.body)
        self.class_sizes.append(class_size)

        if class_size > 30:
            self._add_smell("LargeClass", f"Class '{node.name}' is too large ({class_size} members)",
                          node.lineno, "high")
        elif class_size > 20:
            self._add_smell("LargeClass", f"Class '{node.name}' is moderately large ({class_size} members)",
                          node.lineno, "medium")

        self.generic_visit(node)

    def visit_If(self, node):
        # Check for deeply nested conditions
        depth = self._calculate_nesting_depth(node)
        if depth > 4:
            self._add_smell("DeepNesting", f"Deeply nested if statement (depth: {depth})",
                          node.lineno, "high")
        elif depth > 3:
            self._add_smell("DeepNesting", f"Moderately nested if statement (depth: {depth})",
                          node.lineno, "medium")

        self.generic_visit(node)

    def _add_smell(self, smell_type: str, message: str, line: int, severity: str):
        """Add a code smell if it meets the severity threshold."""
        severity_levels = {"low": 1, "medium": 2, "high": 3}
        threshold = severity_levels.get(self.severity_level, 2)
        current_severity = severity_levels.get(severity, 1)

        if current_severity >= threshold:
            self.smells.append({
                "type": smell_type,
                "message": message,
                "line": line,
                "severity": severity
            })
            self.severity_counts[severity] += 1

    def _calculate_nesting_depth(self, node, depth=0):
        """Calculate nesting depth of control structures."""
        max_depth = depth
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.With)):
                child_depth = self._calculate_nesting_depth(child, depth + 1)
                max_depth = max(max_depth, child_depth)
        return max_depth

    def get_refactoring_suggestions(self) -> List[str]:
        """Get refactoring suggestions based on detected smells."""
        suggestions = []

        if any(smell["type"] == "LongFunction" for smell in self.smells):
            suggestions.append("Consider breaking long functions into smaller, focused functions")

        if any(smell["type"] == "TooManyParameters" for smell in self.smells):
            suggestions.append("Consider using parameter objects or configuration classes")

        if any(smell["type"] == "LargeClass" for smell in self.smells):
            suggestions.append("Consider splitting large classes using Single Responsibility Principle")

        if any(smell["type"] == "DeepNesting" for smell in self.smells):
            suggestions.append("Consider using early returns or extracting nested logic into functions")

        return suggestions


class PerformanceAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing performance issues."""

    def __init__(self):
        self.issues = []
        self.optimizations = []
        self.complexity_score = 0
        self.loop_count = 0
        self.nested_loops = 0

    def visit_For(self, node):
        self.loop_count += 1

        # Check for nested loops
        for child in ast.walk(node):
            if isinstance(child, (ast.For, ast.While)) and child != node:
                self.nested_loops += 1
                self.issues.append({
                    "type": "NestedLoop",
                    "message": "Nested loop detected - potential O(n²) complexity",
                    "line": node.lineno,
                    "severity": "medium"
                })
                break

        self.generic_visit(node)

    def visit_While(self, node):
        self.loop_count += 1
        self.generic_visit(node)

    def visit_ListComp(self, node):
        # List comprehensions are generally good, but check for complexity
        generators = len(node.generators)
        if generators > 2:
            self.issues.append({
                "type": "ComplexListComp",
                "message": "Complex list comprehension - consider breaking down",
                "line": node.lineno,
                "severity": "low"
            })
        else:
            self.optimizations.append({
                "type": "ListComprehension",
                "message": "Good use of list comprehension",
                "line": node.lineno
            })

        self.generic_visit(node)

    def visit_Call(self, node):
        # Check for inefficient operations
        if isinstance(node.func, ast.Attribute):
            if node.func.attr == "append" and self._is_in_loop(node):
                self.issues.append({
                    "type": "AppendInLoop",
                    "message": "List append in loop - consider list comprehension",
                    "line": node.lineno,
                    "severity": "low"
                })

        self.generic_visit(node)

    def _is_in_loop(self, node) -> bool:
        """Check if a node is inside a loop."""
        # This is a simplified check - in practice, you'd need to track the AST hierarchy
        return self.loop_count > 0

    @property
    def complexity_score(self) -> float:
        """Calculate a complexity score based on detected patterns."""
        score = 0
        score += self.loop_count * 2
        score += self.nested_loops * 5
        score += len([issue for issue in self.issues if issue["severity"] == "high"]) * 10
        score += len([issue for issue in self.issues if issue["severity"] == "medium"]) * 5
        score += len([issue for issue in self.issues if issue["severity"] == "low"]) * 1

        return min(100, score)  # Cap at 100
