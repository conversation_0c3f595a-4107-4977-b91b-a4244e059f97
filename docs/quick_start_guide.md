# Quick Start Guide - Advanced AI Agent Framework

This guide will help you get started with the advanced AI agent framework quickly and efficiently.

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd agent-test

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -m agent_framework.cli.main --help
```

## Basic Usage

### 1. Code Analysis

Analyze your code for quality, complexity, and potential issues:

```bash
# Analyze a single file
python -m agent_framework.cli.main analyze --file src/my_code.py --type comprehensive

# Analyze code from stdin
echo "def hello(): pass" | python -m agent_framework.cli.main analyze --stdin --type quality
```

### 2. Code Enhancement

Improve your code with automatic enhancements:

```bash
# Enhance code quality and performance
python -m agent_framework.cli.main enhance --file src/my_code.py --goals quality performance

# Comprehensive enhancement with bug fixing and evaluation
python -m agent_framework.cli.main enhance --file src/my_code.py --comprehensive
```

### 3. Debugging Assistance

Get intelligent debugging help:

```bash
# Debug with error context
python -m agent_framework.cli.main debug --file src/buggy_code.py --auto-fix

# Debug with comprehensive analysis
python -m agent_framework.cli.main debug --file src/buggy_code.py --comprehensive
```

### 4. Code Generation

Generate new code with advanced capabilities:

```bash
# Generate a function
python -m agent_framework.cli.main generate --type function --name "process_data" --description "Process user data"

# Generate a class with comprehensive features
python -m agent_framework.cli.main generate --type class --name "DataProcessor" --comprehensive
```

## Programming Interface

### Basic Setup

```python
from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)

# Configure capabilities
capabilities = AgentCapabilities(
    enable_automatic_bug_fixing=True,
    enable_automatic_evaluation=True,
    enable_advanced_code_generation=True,
    enable_comprehensive_testing=True
)

# Initialize orchestrator
orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
```

### Code Analysis Example

```python
# Analyze code comprehensively
code = '''
def calculate_average(numbers):
    total = sum(numbers)
    return total / len(numbers)
'''

analysis_result = await orchestrator.comprehensive_code_analysis(
    code_content=code,
    file_path="example.py",
    analysis_depth="comprehensive"
)

print(f"Quality Score: {analysis_result['quality_score']}")
print(f"Issues Found: {len(analysis_result['context'].potential_issues)}")
```

### Code Implementation Example

```python
# Implement new functionality
requirements = {
    "type": "function",
    "name": "validate_email",
    "description": "Validate email address format",
    "parameters": ["email"],
    "return_type": "bool"
}

implementation_result = await orchestrator.advanced_code_implementation(
    requirements=requirements,
    file_path="validators.py"
)

if implementation_result["success"]:
    print("✅ Implementation successful!")
    print(f"Generated code:\n{implementation_result['generated_code']}")
```

### Full Advanced Cycle Example

```python
# Run complete enhancement cycle
legacy_code = '''
def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result
'''

enhancement_requirements = {
    "type": "enhancement",
    "goals": ["add_type_hints", "add_error_handling", "improve_performance"],
    "description": "Modernize legacy data processing function"
}

results = await orchestrator.run_full_advanced_cycle(
    code_content=legacy_code,
    file_path="data_processor.py",
    requirements=enhancement_requirements
)

print(f"✅ Enhancement completed: {results['overall_success']}")
if results["implementation"]:
    print(f"📈 Quality improved: {results['implementation']['evaluation'].overall_score}")
```

## Advanced Features

### Automatic Bug Fixing

```python
# Enable automatic bug fixing
capabilities = AgentCapabilities(
    enable_automatic_bug_fixing=True,
    max_fix_iterations=5
)

orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

# The orchestrator will automatically attempt to fix bugs during code operations
```

### Quality Evaluation

```python
# Enable comprehensive evaluation
capabilities = AgentCapabilities(
    enable_automatic_evaluation=True,
    evaluation_on_every_change=True,
    rollback_on_critical_issues=True
)

# Evaluation will run automatically and provide detailed quality metrics
```

### Multi-Agent Coordination

```python
# The framework automatically coordinates multiple specialized agents:
# - Code Analysis Agent: Deep code understanding
# - Code Editor Agent: Safe code modifications
# - Debugger Agent: Intelligent error resolution
# - Testing Agent: Comprehensive test generation
# - Optimization Agent: Performance improvements
```

## Configuration

### Environment Variables

```bash
# Set default model configuration
export OPENAI_API_KEY="your-api-key"
export ANTHROPIC_API_KEY="your-api-key"

# Configure logging level
export LOG_LEVEL="INFO"

# Set working directory
export AGENT_WORKSPACE="/path/to/your/project"
```

### Configuration File

Create `config.yaml`:

```yaml
framework:
  log_level: INFO
  workspace_path: "/path/to/project"

advanced_capabilities:
  enabled: true
  enable_automatic_bug_fixing: true
  enable_automatic_evaluation: true
  enable_advanced_code_generation: true
  enable_comprehensive_testing: true
  max_fix_iterations: 5
  evaluation_on_every_change: true
  rollback_on_critical_issues: true

agents:
  code_analyzer:
    model: "gpt-4"
    temperature: 0.1
  
  code_editor:
    model: "claude-3-sonnet"
    temperature: 0.2
  
  debugger:
    model: "gpt-4"
    temperature: 0.1
```

## Common Use Cases

### 1. Legacy Code Modernization

```bash
# Modernize old Python code
python -m agent_framework.cli.main enhance \
  --file legacy_module.py \
  --goals modernize type_hints error_handling \
  --comprehensive
```

### 2. Code Quality Improvement

```bash
# Improve code quality with comprehensive analysis
python -m agent_framework.cli.main enhance \
  --file src/ \
  --goals quality maintainability documentation \
  --enable-evaluation
```

### 3. Bug Detection and Fixing

```bash
# Automatically detect and fix bugs
python -m agent_framework.cli.main debug \
  --file problematic_code.py \
  --auto-fix \
  --max-iterations 3
```

### 4. Test Generation

```bash
# Generate comprehensive tests
python -m agent_framework.cli.main generate \
  --type tests \
  --file src/my_module.py \
  --comprehensive
```

## Next Steps

1. **Explore Examples**: Check the `examples/` directory for more detailed examples
2. **Read Full Documentation**: See `docs/enhanced_capabilities_guide.md` for complete feature documentation
3. **Multi-Agent Setup**: Learn about multi-agent coordination in `docs/multi_agent_guide.md`
4. **Architecture**: Understand the framework architecture in `docs/architecture.md`

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`
2. **API Key Issues**: Set your API keys in environment variables
3. **Permission Errors**: Ensure the framework has write access to your project directory
4. **Memory Issues**: For large codebases, consider using `--analysis-depth basic` initially

### Getting Help

- Check the documentation in the `docs/` directory
- Run commands with `--help` for detailed usage information
- Review the examples in `examples/` directory
- Check the test files for usage patterns

## Performance Tips

1. **Use Appropriate Analysis Depth**: Start with "basic" for large codebases
2. **Enable Caching**: The framework caches analysis results for better performance
3. **Batch Operations**: Process multiple files together when possible
4. **Configure Resource Limits**: Set appropriate timeout and iteration limits
