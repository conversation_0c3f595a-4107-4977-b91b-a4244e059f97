"""
Tests for the TaskExecutor component with enhanced capabilities.
"""

import asyncio
import pytest
import time
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

from agent_framework.core.types import Task, TaskResult, TaskStatus, TaskPriority
from agent_framework.execution.executor import TaskExecutor, TaskExecutionMetrics, TaskValidationResult


class TestTaskExecutor:
    """Test cases for TaskExecutor."""

    @pytest.mark.asyncio
    async def test_executor_initialization(self, task_executor):
        """Test that the executor initializes correctly."""
        assert task_executor._is_initialized
        assert task_executor._is_running
        assert len(task_executor._workers) == 2  # Based on test config
        assert task_executor._task_queue is not None

    @pytest.mark.asyncio
    async def test_execute_simple_task(self, task_executor, sample_task):
        """Test executing a simple task."""
        result = await task_executor.execute_task(sample_task)

        assert isinstance(result, TaskResult)
        assert result.task_id == sample_task.id
        assert result.status == TaskStatus.COMPLETED
        assert result.result is not None
        assert result.execution_time is not None
        assert result.execution_time > 0

    @pytest.mark.asyncio
    async def test_execute_code_analysis_task(self, task_executor, code_analysis_task):
        """Test executing a code analysis task."""
        result = await task_executor.execute_task(code_analysis_task)

        assert result.status == TaskStatus.COMPLETED
        assert "Code analysis completed" in result.result
        assert result.execution_time > 0

    @pytest.mark.asyncio
    async def test_task_priority_ordering(self, task_executor, sample_task, high_priority_task):
        """Test that high priority tasks are executed first."""
        # Create multiple tasks with different priorities
        low_priority_task = Task(
            name="Low Priority",
            task_type="test",
            priority=TaskPriority.LOW
        )

        # Submit tasks in reverse priority order
        tasks = [low_priority_task, sample_task, high_priority_task]
        results = await asyncio.gather(*[
            task_executor.execute_task(task) for task in tasks
        ])

        # All tasks should complete successfully
        for result in results:
            assert result.status == TaskStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, task_executor):
        """Test that multiple tasks can be executed concurrently."""
        # Create multiple tasks
        tasks = [
            Task(name=f"Task {i}", task_type="test", parameters={"id": i})
            for i in range(5)
        ]

        start_time = time.time()
        results = await asyncio.gather(*[
            task_executor.execute_task(task) for task in tasks
        ])
        end_time = time.time()

        # All tasks should complete
        assert len(results) == 5
        for result in results:
            assert result.status == TaskStatus.COMPLETED

        # Should take less time than sequential execution
        # (allowing for some overhead)
        assert end_time - start_time < 3.0

    @pytest.mark.asyncio
    async def test_task_timeout(self, test_config, message_broker):
        """Test task timeout functionality."""
        # Create executor with very short timeout
        test_config.execution.task_timeout_seconds = 0.1
        executor = TaskExecutor(test_config, message_broker)
        await executor.initialize()

        try:
            # Create a task that would take longer than timeout
            long_task = Task(
                name="Long Task",
                task_type="test",
                timeout_seconds=0.05  # Very short timeout
            )

            # Mock the task logic to take longer than timeout
            async def slow_task_logic(task):
                await asyncio.sleep(1.0)  # Takes 1 second, much longer than 0.05s timeout
                return "Should not reach here"

            with patch.object(executor, '_run_task_logic', side_effect=slow_task_logic):
                result = await executor.execute_task(long_task)

                assert result.status == TaskStatus.FAILED
                assert "timed out" in result.error.lower()

        finally:
            await executor.shutdown()

    @pytest.mark.asyncio
    async def test_task_error_handling(self, task_executor):
        """Test error handling in task execution."""
        # Mock the task logic to raise an exception
        async def failing_task_logic(task):
            raise ValueError("Test error")

        with patch.object(task_executor, '_run_task_logic', side_effect=failing_task_logic):
            error_task = Task(name="Error Task", task_type="test")
            result = await task_executor.execute_task(error_task)

            assert result.status == TaskStatus.FAILED
            assert "Test error" in result.error

    @pytest.mark.asyncio
    async def test_get_active_task_count(self, task_executor):
        """Test getting active task count."""
        initial_count = await task_executor.get_active_task_count()
        assert initial_count == 0

        # Test that the method works and returns an integer
        # The actual count may vary due to async execution timing
        assert isinstance(initial_count, int)
        assert initial_count >= 0

    @pytest.mark.asyncio
    async def test_get_queue_size(self, task_executor):
        """Test getting queue size."""
        initial_size = await task_executor.get_queue_size()
        assert initial_size == 0

        # The queue size is hard to test directly since tasks are processed quickly
        # This test mainly ensures the method works
        assert isinstance(initial_size, int)

    @pytest.mark.asyncio
    async def test_cancel_task(self, task_executor):
        """Test task cancellation."""
        # Test cancelling a non-existent task
        non_existent_id = "00000000-0000-0000-0000-000000000000"
        cancelled = await task_executor.cancel_task(non_existent_id)
        assert not cancelled  # Should return False for non-existent task

        # Test that the cancel_task method works
        task = Task(name="Test Task", task_type="test")
        # For a quick task, cancellation might not be possible, but method should work
        result = await task_executor.cancel_task(str(task.id))
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_task_retry_logic(self, task_executor):
        """Test task retry functionality."""
        # Note: Current executor implementation doesn't have retry logic
        # This test verifies that tasks with max_retries set still work

        async def failing_logic(task):
            raise ValueError("Task failure")

        with patch.object(task_executor, '_run_task_logic', side_effect=failing_logic):
            retry_task = Task(
                name="Retry Task",
                task_type="test",
                max_retries=2  # This parameter is accepted but not used yet
            )

            result = await task_executor.execute_task(retry_task)

            # Should fail since retry logic is not implemented yet
            assert result.status == TaskStatus.FAILED
            assert "Task failure" in result.error

    @pytest.mark.asyncio
    async def test_different_task_types(self, task_executor):
        """Test execution of different task types."""
        task_configs = {
            "code_analysis": {"code": "def test(): pass"},
            "refactoring": {"code": "def test(): pass", "target": "improve"},
            "test_generation": {"code": "def test(): pass"},
            "generic": {"test": True}
        }

        for task_type, params in task_configs.items():
            task = Task(
                name=f"{task_type} Task",
                task_type=task_type,
                parameters=params
            )

            result = await task_executor.execute_task(task)
            assert result.status == TaskStatus.COMPLETED
            assert result.result is not None

    @pytest.mark.asyncio
    async def test_task_metadata_preservation(self, task_executor, sample_task):
        """Test that task metadata is preserved through execution."""
        original_name = sample_task.name
        original_description = sample_task.description
        original_parameters = sample_task.parameters.copy()

        result = await task_executor.execute_task(sample_task)

        # Task metadata should be preserved
        assert sample_task.name == original_name
        assert sample_task.description == original_description
        assert sample_task.parameters == original_parameters

        # Task should have execution timestamps
        assert sample_task.started_at is not None
        assert sample_task.completed_at is not None
        assert sample_task.completed_at >= sample_task.started_at

    @pytest.mark.asyncio
    async def test_executor_shutdown(self, test_config, message_broker):
        """Test executor shutdown process."""
        executor = TaskExecutor(test_config, message_broker)
        await executor.initialize()

        assert executor._is_initialized
        assert executor._is_running

        await executor.shutdown()

        assert not executor._is_initialized
        assert not executor._is_running
        assert len(executor._workers) == 0
        assert len(executor._active_tasks) == 0


class TestEnhancedTaskExecutor:
    """Test cases for enhanced TaskExecutor functionality."""

    @pytest.mark.asyncio
    async def test_task_validation_basic(self, task_executor):
        """Test basic task validation."""
        # Test valid task
        valid_task = Task(
            name="test_task",
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={}
        )

        validation_result = await task_executor._validate_task_comprehensive(valid_task)
        assert validation_result.is_valid
        assert len(validation_result.errors) == 0

    @pytest.mark.asyncio
    async def test_task_validation_missing_name(self, task_executor):
        """Test validation fails for task without name."""
        invalid_task = Task(
            name="",  # Empty name should fail validation
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={}
        )

        validation_result = await task_executor._validate_task_comprehensive(invalid_task)
        assert not validation_result.is_valid
        assert any("name is required" in error for error in validation_result.errors)

    @pytest.mark.asyncio
    async def test_task_validation_missing_parameters(self, task_executor):
        """Test validation for missing required parameters."""
        task_with_missing_params = Task(
            name="code_analysis_task",
            task_type="code_analysis",
            priority=TaskPriority.NORMAL,
            parameters={}  # Missing 'code' parameter
        )

        validation_result = await task_executor._validate_task_comprehensive(task_with_missing_params)
        assert not validation_result.is_valid
        assert any("Required parameter 'code' missing" in error for error in validation_result.errors)

    @pytest.mark.asyncio
    async def test_system_capacity_check(self, task_executor):
        """Test system capacity checking."""
        # Initially should have capacity
        has_capacity = await task_executor._check_system_capacity()
        assert has_capacity

        # Fill up active tasks to max capacity
        max_tasks = task_executor.config.execution.max_concurrent_tasks
        for i in range(max_tasks):
            mock_task = AsyncMock()
            task_executor._active_tasks[f"task_{i}"] = mock_task

        # Should not have capacity now
        has_capacity = await task_executor._check_system_capacity()
        assert not has_capacity

    @pytest.mark.asyncio
    async def test_metrics_tracking(self, task_executor, sample_task):
        """Test that metrics are properly tracked."""
        initial_metrics = task_executor.get_metrics()
        assert initial_metrics.total_tasks == 0
        assert initial_metrics.completed_tasks == 0

        # Execute a task
        result = await task_executor.execute_task(sample_task)
        assert result.status == TaskStatus.COMPLETED

        # Check metrics updated
        updated_metrics = task_executor.get_metrics()
        assert updated_metrics.total_tasks == 1
        assert updated_metrics.completed_tasks == 1
        assert updated_metrics.average_execution_time > 0

    @pytest.mark.asyncio
    async def test_error_handling_and_retry_logic(self, task_executor, sample_task):
        """Test error handling and retry recommendations."""
        # Test timeout error handling
        timeout_error = asyncio.TimeoutError("Task timed out")
        should_retry = task_executor._should_retry_task(sample_task, timeout_error)
        assert should_retry

        # Test validation error handling (should not retry)
        validation_error = ValueError("Invalid parameters")
        should_retry = task_executor._should_retry_task(sample_task, validation_error)
        assert not should_retry

    @pytest.mark.asyncio
    async def test_task_timeout_handling(self, task_executor):
        """Test task timeout handling."""
        # Create a task with very short timeout
        timeout_task = Task(
            name="timeout_test",
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={},
            timeout_seconds=0.1  # Very short timeout
        )

        # Mock the task logic to take longer than timeout
        async def slow_task_logic(task):
            await asyncio.sleep(0.2)  # Longer than timeout
            return "Should not reach here"

        with patch.object(task_executor, '_run_task_logic', side_effect=slow_task_logic):
            result = await task_executor.execute_task(timeout_task)

            assert result.status == TaskStatus.FAILED
            assert "timed out" in result.error.lower()

    @pytest.mark.asyncio
    async def test_health_check(self, task_executor):
        """Test comprehensive health check."""
        health_status = await task_executor.health_check()

        assert health_status['status'] == 'healthy'
        assert isinstance(health_status['issues'], list)
        assert isinstance(health_status['warnings'], list)

    @pytest.mark.asyncio
    async def test_system_status_reporting(self, task_executor):
        """Test system status reporting."""
        status = task_executor.get_system_status()

        assert 'is_running' in status
        assert 'active_tasks' in status
        assert 'queue_size' in status
        assert 'metrics' in status
        assert 'error_counts' in status
        assert 'performance_thresholds' in status

        # Check metrics structure
        metrics = status['metrics']
        assert 'total_tasks' in metrics
        assert 'completed_tasks' in metrics
        assert 'failed_tasks' in metrics
        assert 'error_rate' in metrics

    @pytest.mark.asyncio
    async def test_performance_threshold_configuration(self, task_executor):
        """Test performance threshold configuration."""
        # Configure new thresholds
        task_executor.configure_performance_thresholds(
            max_execution_time=600,
            max_error_rate=0.05
        )

        assert task_executor._performance_thresholds['max_execution_time'] == 600
        assert task_executor._performance_thresholds['max_error_rate'] == 0.05

    @pytest.mark.asyncio
    async def test_metrics_reset(self, task_executor, sample_task):
        """Test metrics reset functionality."""
        # Execute a task to generate metrics
        await task_executor.execute_task(sample_task)

        metrics_before = task_executor.get_metrics()
        assert metrics_before.total_tasks > 0

        # Reset metrics
        task_executor.reset_metrics()

        metrics_after = task_executor.get_metrics()
        assert metrics_after.total_tasks == 0
        assert metrics_after.completed_tasks == 0

    @pytest.mark.asyncio
    async def test_graceful_shutdown_with_timeout(self, test_config):
        """Test graceful shutdown with timeout."""
        executor = TaskExecutor(test_config)
        await executor.initialize()

        # Test successful shutdown
        success = await executor.graceful_shutdown_with_timeout(timeout_seconds=5.0)
        assert success

    @pytest.mark.asyncio
    async def test_custom_task_validator(self, task_executor):
        """Test adding custom task validators."""
        # Add a custom validator
        def custom_validator(task: Task) -> TaskValidationResult:
            errors = []
            if task.name.startswith("invalid_"):
                errors.append("Task name cannot start with 'invalid_'")
            return TaskValidationResult(is_valid=len(errors) == 0, errors=errors)

        task_executor.add_task_validator(custom_validator)

        # Test with invalid task name
        invalid_task = Task(
            name="invalid_test",
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={}
        )

        validation_result = await task_executor._validate_task_comprehensive(invalid_task)
        assert not validation_result.is_valid
        assert any("cannot start with 'invalid_'" in error for error in validation_result.errors)

    @pytest.mark.asyncio
    async def test_enhanced_error_metadata(self, task_executor):
        """Test that enhanced error handling includes proper metadata."""
        # Create a task that will fail
        failing_task = Task(
            name="failing_task",
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={}
        )

        # Mock task logic to raise an exception
        async def failing_task_logic(task):
            raise RuntimeError("Simulated task failure")

        with patch.object(task_executor, '_run_task_logic', side_effect=failing_task_logic):
            result = await task_executor.execute_task(failing_task)

            assert result.status == TaskStatus.FAILED
            assert result.metadata is not None
            assert 'error_type' in result.metadata
            assert 'worker' in result.metadata
            assert 'retry_recommended' in result.metadata