"""
Test suite for per-agent model configuration functionality.

Tests cover model client factory, configuration validation, error handling,
fallback mechanisms, and AutoGen integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from agent_framework.core.config import ModelConfig, Model<PERSON><PERSON>ider, AgentRoleConfig
from agent_framework.core.model_client_factory import ModelClientFactory, model_client_factory
from agent_framework.core.error_handling import (
    ModelClientError, ConfigurationError, ErrorCategory, ErrorSeverity
)
from agent_framework.agents.base_agent import BaseAgent


class TestModelConfig:
    """Test model configuration validation and creation."""
    
    def test_model_config_creation(self):
        """Test basic model configuration creation."""
        config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="test-key",
            temperature=0.7
        )
        
        assert config.provider == ModelProvider.OPENAI
        assert config.model == "gpt-4"
        assert config.api_key == "test-key"
        assert config.temperature == 0.7
    
    def test_model_config_with_fallbacks(self):
        """Test model configuration with fallback configs."""
        fallback_config = ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="fallback-key"
        )
        
        primary_config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="primary-key",
            fallback_configs=[fallback_config]
        )
        
        assert len(primary_config.fallback_configs) == 1
        assert primary_config.fallback_configs[0].provider == ModelProvider.ANTHROPIC
    
    def test_model_config_validation(self):
        """Test model configuration validation."""
        # Test API key validation from environment
        with patch.dict('os.environ', {'OPENAI_API_KEY': 'env-key'}):
            config = ModelConfig(provider=ModelProvider.OPENAI, model="gpt-4")
            # Trigger validation
            validated_config = ModelConfig.parse_obj(config.dict())
            assert validated_config.api_key == 'env-key'
    
    def test_base_url_validation(self):
        """Test base URL validation for different providers."""
        config = ModelConfig(provider=ModelProvider.OPENAI, model="gpt-4")
        # Trigger validation
        validated_config = ModelConfig.parse_obj(config.dict())
        assert validated_config.base_url == "https://api.openai.com/v1"


class TestModelClientFactory:
    """Test model client factory functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.factory = ModelClientFactory()
    
    def test_factory_initialization(self):
        """Test factory initialization and client registry."""
        factory = ModelClientFactory()
        supported_providers = factory.get_supported_providers()
        
        # Should have at least some providers registered
        assert len(supported_providers) > 0
        assert ModelProvider.OPENAI in supported_providers or len(supported_providers) == 0  # Depends on imports
    
    def test_provider_support_check(self):
        """Test provider support checking."""
        factory = ModelClientFactory()
        
        # Mock a supported provider
        factory._client_registry[ModelProvider.OPENAI] = Mock
        
        assert factory.is_provider_supported(ModelProvider.OPENAI)
        assert not factory.is_provider_supported(ModelProvider.ANTHROPIC)  # Assuming not registered
    
    def test_custom_client_registration(self):
        """Test custom client registration."""
        factory = ModelClientFactory()
        mock_client_class = Mock()
        
        factory.register_client(ModelProvider.OPENAI, mock_client_class)
        
        assert factory.is_provider_supported(ModelProvider.OPENAI)
        assert factory._client_registry[ModelProvider.OPENAI] == mock_client_class
    
    @patch('agent_framework.core.model_client_factory.OpenAIChatCompletionClient')
    def test_openai_client_creation(self, mock_openai_client):
        """Test OpenAI client creation."""
        factory = ModelClientFactory()
        mock_instance = Mock()
        mock_openai_client.return_value = mock_instance
        
        config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="test-key",
            temperature=0.7
        )
        
        # Mock the client class in registry
        factory._client_registry[ModelProvider.OPENAI] = mock_openai_client
        
        client = factory.create_client(config)
        
        assert client == mock_instance
        mock_openai_client.assert_called_once()
    
    def test_unsupported_provider_error(self):
        """Test error handling for unsupported providers."""
        factory = ModelClientFactory()
        
        config = ModelConfig(
            provider=ModelProvider.ANTHROPIC,  # Assuming not supported
            model="claude-3-5-sonnet-20241022",
            api_key="test-key"
        )
        
        with pytest.raises(Exception):  # Should raise ConfigurationError
            factory.create_client(config)
    
    def test_fallback_mechanism(self):
        """Test fallback configuration mechanism."""
        factory = ModelClientFactory()
        
        # Mock successful fallback client
        mock_fallback_client = Mock()
        factory._client_registry[ModelProvider.OPENROUTER] = Mock(return_value=mock_fallback_client)
        
        fallback_config = ModelConfig(
            provider=ModelProvider.OPENROUTER,
            model="qwen/qwen3-coder:free",
            api_key="fallback-key"
        )
        
        primary_config = ModelConfig(
            provider=ModelProvider.OPENAI,  # Will fail if not registered
            model="gpt-4",
            api_key="primary-key",
            fallback_configs=[fallback_config]
        )
        
        # Should fall back to OpenRouter
        try:
            client = factory.create_client(primary_config)
            # If we get here, fallback worked
            assert client is not None
        except Exception:
            # Expected if no providers are actually available
            pass


class TestBaseAgent:
    """Test base agent with per-agent model configuration."""
    
    def test_agent_model_config_resolution(self):
        """Test agent model configuration resolution."""
        agent_model_config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="agent-key"
        )
        
        global_model_config = ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="global-key"
        )
        
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test message",
            model_configuration=agent_model_config
        )
        
        agent = BaseAgent(role_config, global_model_config=global_model_config)
        
        # Should use agent-specific config
        assert agent.effective_model_config == agent_model_config
    
    def test_agent_fallback_to_global_config(self):
        """Test agent falling back to global model configuration."""
        global_model_config = ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="global-key"
        )
        
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test message"
            # No model_config specified
        )
        
        agent = BaseAgent(role_config, global_model_config=global_model_config)
        
        # Should use global config
        assert agent.effective_model_config == global_model_config
    
    def test_agent_no_model_config(self):
        """Test agent with no model configuration."""
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test message"
        )
        
        agent = BaseAgent(role_config)
        
        # Should have no effective config
        assert agent.effective_model_config is None
    
    def test_backward_compatibility(self):
        """Test backward compatibility with legacy initialization."""
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test message"
        )
        
        # Should not raise error with additional kwargs
        agent = BaseAgent(role_config, legacy_param="value", another_param=123)
        
        assert agent.role_config.name == "test_agent"


class TestErrorHandling:
    """Test error handling and recovery mechanisms."""
    
    def test_model_client_error_creation(self):
        """Test model client error creation and classification."""
        config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="test-key"
        )
        
        error = ModelClientError("Test error", model_config=config)
        
        assert error.category == ErrorCategory.MODEL_CLIENT
        assert error.severity == ErrorSeverity.HIGH
        assert error.context.model_config == config
    
    def test_configuration_error_creation(self):
        """Test configuration error creation."""
        error = ConfigurationError("Invalid config", config_field="api_key")
        
        assert error.category == ErrorCategory.CONFIGURATION
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.context.additional_info["config_field"] == "api_key"
    
    def test_error_handler_classification(self):
        """Test error handler classification."""
        from agent_framework.core.error_handling import error_handler
        
        # Test authentication error classification
        auth_error = Exception("api_key is invalid")
        classified = error_handler._classify_error(auth_error)
        
        assert classified.category == ErrorCategory.AUTHENTICATION
    
    def test_error_statistics(self):
        """Test error statistics tracking."""
        from agent_framework.core.error_handling import error_handler
        
        # Clear history first
        error_handler.clear_error_history()
        
        # Add some test errors
        error1 = ModelClientError("Test error 1")
        error2 = ConfigurationError("Test error 2")
        
        error_handler._error_history.extend([error1, error2])
        
        stats = error_handler.get_error_statistics()
        
        assert stats["total_errors"] == 2
        assert ErrorCategory.MODEL_CLIENT.value in stats["by_category"]
        assert ErrorCategory.CONFIGURATION.value in stats["by_category"]


class TestIntegration:
    """Integration tests for the complete per-agent model system."""
    
    @pytest.mark.asyncio
    async def test_agent_initialization_with_model_config(self):
        """Test agent initialization with model configuration."""
        model_config = ModelConfig(
            provider=ModelProvider.OPENROUTER,  # Use a provider that doesn't require real API
            model="test-model",
            api_key="test-key",
            base_url="http://localhost:8000"  # Mock endpoint
        )
        
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test message",
            model_config=model_config
        )
        
        agent = BaseAgent(role_config)
        
        # Mock the model client factory to avoid real API calls
        with patch.object(agent, '_initialize_model_client') as mock_init:
            mock_init.return_value = None
            
            try:
                await agent.initialize({})
                # If we get here, initialization succeeded
                assert True
            except Exception as e:
                # Expected if dependencies are not available
                assert "not initialized" in str(e) or "not available" in str(e)
    
    def test_multi_agent_config_creation(self):
        """Test creation of multi-agent configuration with different models."""
        from agent_framework.core.config import MultiAgentConfig
        
        openai_config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4",
            api_key="openai-key"
        )
        
        anthropic_config = ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="anthropic-key"
        )
        
        multi_config = MultiAgentConfig(
            enabled=True,
            agent_roles={
                "analyst": AgentRoleConfig(
                    name="analyst",
                    description="Code analyst",
                    system_message="Analyze code",
                    model_configuration=openai_config
                ),
                "writer": AgentRoleConfig(
                    name="writer",
                    description="Documentation writer",
                    system_message="Write docs",
                    model_configuration=anthropic_config
                )
            }
        )
        
        assert len(multi_config.agent_roles) == 2
        assert multi_config.agent_roles["analyst"].model_configuration.provider == ModelProvider.OPENAI
        assert multi_config.agent_roles["writer"].model_configuration.provider == ModelProvider.ANTHROPIC


if __name__ == "__main__":
    pytest.main([__file__])
