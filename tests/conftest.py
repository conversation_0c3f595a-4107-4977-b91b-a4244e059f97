"""
Test configuration and fixtures for the agent framework tests.
"""

import asyncio
import pytest
import pytest_asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

from agent_framework.core.config import FrameworkConfig
from agent_framework.core.types import Task, TaskPriority, PluginInterface, PluginRequest, PluginResponse, PluginCapability
from agent_framework.communication.broker import Message<PERSON>roker
from agent_framework.context.manager import ContextManager
from agent_framework.execution.executor import TaskExecutor
from agent_framework.plugins.manager import PluginManager


@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_config():
    """Create a test configuration."""
    config = FrameworkConfig()
    config.model.api_key = "test-api-key"
    config.execution.max_concurrent_tasks = 2
    config.execution.task_timeout_seconds = 5
    config.plugins.auto_load_plugins = False
    config.plugins.plugin_directories = []
    return config


@pytest_asyncio.fixture
async def message_broker(test_config):
    """Create and initialize a message broker for testing."""
    broker = MessageBroker(test_config)
    await broker.initialize()
    try:
        yield broker
    finally:
        await broker.shutdown()


@pytest_asyncio.fixture
async def context_manager(test_config):
    """Create and initialize a context manager for testing."""
    broker = MessageBroker(test_config)
    await broker.initialize()
    try:
        manager = ContextManager(test_config, broker)
        await manager.initialize()
        try:
            yield manager
        finally:
            await manager.shutdown()
    finally:
        await broker.shutdown()


@pytest_asyncio.fixture
async def task_executor(test_config):
    """Create and initialize a task executor for testing."""
    broker = MessageBroker(test_config)
    await broker.initialize()
    try:
        executor = TaskExecutor(test_config, broker)
        await executor.initialize()
        try:
            yield executor
        finally:
            await executor.shutdown()
    finally:
        await broker.shutdown()


@pytest_asyncio.fixture
async def plugin_manager(test_config):
    """Create and initialize a plugin manager for testing."""
    broker = MessageBroker(test_config)
    await broker.initialize()
    try:
        manager = PluginManager(test_config, broker)
        await manager.initialize()
        try:
            yield manager
        finally:
            await manager.shutdown()
    finally:
        await broker.shutdown()


@pytest.fixture
def sample_task():
    """Create a sample task for testing."""
    return Task(
        name="Test Task",
        description="A test task",
        task_type="test",
        priority=TaskPriority.NORMAL,
        parameters={"test_param": "test_value"}
    )


@pytest.fixture
def high_priority_task():
    """Create a high priority task for testing."""
    return Task(
        name="High Priority Task",
        description="A high priority test task",
        task_type="test",
        priority=TaskPriority.HIGH,
        parameters={"urgent": True}
    )


@pytest.fixture
def code_analysis_task():
    """Create a code analysis task for testing."""
    return Task(
        name="Code Analysis Task",
        description="Analyze code complexity",
        task_type="code_analysis",
        priority=TaskPriority.NORMAL,
        parameters={
            "code": """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
"""
        }
    )


class MockPlugin(PluginInterface):
    """Mock plugin for testing."""

    PLUGIN_NAME = "mock_plugin"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "A mock plugin for testing"

    def __init__(self):
        self._initialized = False
        self._config = {}

    @property
    def name(self) -> str:
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        return self.PLUGIN_VERSION

    async def initialize(self, config):
        self._config = config
        self._initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        if not self._initialized:
            return PluginResponse(success=False, error="Plugin not initialized")

        if request.capability == "test_capability":
            return PluginResponse(
                success=True,
                result={"message": "Mock plugin executed successfully"},
                execution_time=0.1
            )
        else:
            return PluginResponse(
                success=False,
                error=f"Unknown capability: {request.capability}"
            )

    async def get_capabilities(self):
        return [
            PluginCapability(
                name="test_capability",
                description="A test capability",
                input_schema={"type": "object"},
                output_schema={"type": "object"}
            )
        ]

    async def cleanup(self):
        self._initialized = False
        self._config.clear()


@pytest.fixture
def mock_plugin():
    """Create a mock plugin for testing."""
    return MockPlugin()


@pytest.fixture
def temp_plugin_dir():
    """Create a temporary directory for plugin testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        plugin_dir = Path(temp_dir)

        # Create a simple test plugin file
        plugin_file = plugin_dir / "test_plugin.py"
        plugin_code = '''
from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability

class TestPlugin(PluginInterface):
    PLUGIN_NAME = "test_plugin"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "A test plugin"

    @property
    def name(self):
        return self.PLUGIN_NAME

    @property
    def version(self):
        return self.PLUGIN_VERSION

    async def initialize(self, config):
        pass

    async def execute(self, request):
        return PluginResponse(success=True, result="test")

    async def get_capabilities(self):
        return []

    async def cleanup(self):
        pass
'''
        plugin_file.write_text(plugin_code)
        yield str(plugin_dir)