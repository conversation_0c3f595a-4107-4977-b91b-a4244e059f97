"""
Tests for the CLI interface.

This module contains tests for the command-line interface functionality
including command parsing, execution, and output formatting.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from agent_framework.cli.core import Agent<PERSON><PERSON>
from agent_framework.cli.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ProgressIndicator
from agent_framework.cli.commands.analyze import AnalyzeCommand
from agent_framework.cli.commands.generate import GenerateCommand


class TestCLIUtils:
    """Test CLI utility functions."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.utils = CLIUtils()
    
    def test_print_methods(self, capsys):
        """Test various print methods."""
        self.utils.print_success("Success message")
        self.utils.print_error("Error message")
        self.utils.print_warning("Warning message")
        self.utils.print_info("Info message")
        
        captured = capsys.readouterr()
        assert "Success message" in captured.out
        assert "Error message" in captured.err
        assert "Warning message" in captured.out
        assert "Info message" in captured.out
    
    def test_print_table(self, capsys):
        """Test table printing functionality."""
        headers = ["Name", "Type", "Value"]
        rows = [
            ["item1", "string", "value1"],
            ["item2", "number", "42"],
            ["item3", "boolean", "true"]
        ]
        
        self.utils.print_table(headers, rows, "Test Table")
        
        captured = capsys.readouterr()
        assert "Test Table" in captured.out
        assert "Name" in captured.out
        assert "item1" in captured.out
        assert "42" in captured.out
    
    def test_print_code_block(self, capsys):
        """Test code block printing."""
        code = "def hello():\n    print('Hello, World!')"
        self.utils.print_code_block(code, "python", "Example Function")
        
        captured = capsys.readouterr()
        assert "Example Function" in captured.out
        assert "```python" in captured.out
        assert "def hello()" in captured.out


class TestProgressIndicator:
    """Test progress indicator functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.progress = ProgressIndicator()
    
    def test_spinner_context_manager(self):
        """Test spinner context manager."""
        import time
        
        with self.progress.spinner("Testing..."):
            time.sleep(0.1)  # Brief pause to see spinner
        
        # Test that spinner stops properly
        assert not self.progress._stop_event.is_set() or True  # Event may be cleared
    
    def test_progress_bar(self, capsys):
        """Test progress bar display."""
        self.progress.progress_bar(50, 100, "Processing...")
        captured = capsys.readouterr()
        assert "50%" in captured.out
        assert "Processing..." in captured.out
    
    def test_step_progress(self, capsys):
        """Test step progress display."""
        self.progress.step_progress(3, 5, "Step 3 of 5")
        captured = capsys.readouterr()
        assert "[3/5]" in captured.out
        assert "Step 3 of 5" in captured.out


class TestAnalyzeCommand:
    """Test analyze command functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.command = AnalyzeCommand()
    
    def test_command_properties(self):
        """Test command basic properties."""
        assert self.command.name == "analyze"
        assert "analyze" in self.command.description.lower()
        assert len(self.command.get_help_text()) > 0
    
    def test_add_arguments(self):
        """Test argument parser setup."""
        import argparse
        parser = argparse.ArgumentParser()
        
        # Should not raise an exception
        self.command.add_arguments(parser)
        
        # Test that required argument groups are added
        # This is a basic test - in practice you'd test specific arguments
        assert len(parser._action_groups) > 0
    
    @pytest.mark.asyncio
    async def test_execute_with_mock_orchestrator(self):
        """Test command execution with mocked orchestrator."""
        # Create mock orchestrator
        mock_orchestrator = AsyncMock()
        mock_config = Mock()
        
        # Create mock args
        class MockArgs:
            def __init__(self):
                self.list_plugins = False
                self.code = "def hello(): pass"
                self.file = None
                self.stdin = False
                self.type = "complexity"
                self.detailed = False
                self.threshold = 10
                self.format = "text"
                self.output = None
                self.pretty = False
        
        args = MockArgs()
        
        # Mock the plugin request handling
        with patch.object(self.command, 'handle_plugin_request') as mock_handle:
            mock_handle.return_value = {
                "success": True,
                "result": {"complexity": 5}
            }
            
            result = await self.command.execute(args, mock_orchestrator, mock_config)
            
            assert result["success"] is True
            assert "Analysis completed" in result["message"]


class TestGenerateCommand:
    """Test generate command functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.command = GenerateCommand()
    
    def test_command_properties(self):
        """Test command basic properties."""
        assert self.command.name == "generate"
        assert "generate" in self.command.description.lower()
        assert len(self.command.get_help_text()) > 0
    
    @pytest.mark.asyncio
    async def test_execute_function_generation(self):
        """Test function generation."""
        mock_orchestrator = AsyncMock()
        mock_config = Mock()
        
        class MockArgs:
            def __init__(self):
                self.list_plugins = False
                self.generate_type = "function"
                self.name = "test_function"
                self.description = "A test function"
                self.parameters = ["param1:str", "param2:int:42"]
                self.return_type = "str"
                self.docstring_style = "google"
                self.format = "text"
                self.output = None
                self.pretty = False
        
        args = MockArgs()
        
        with patch.object(self.command, 'handle_plugin_request') as mock_handle:
            mock_handle.return_value = {
                "success": True,
                "result": {"code": "def test_function(param1: str, param2: int = 42) -> str:\n    pass"}
            }
            
            result = await self.command.execute(args, mock_orchestrator, mock_config)

            assert result["success"] is True
            assert "function" in result["message"]


class TestAgentCLI:
    """Test main CLI application."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.cli = AgentCLI()
    
    def test_create_parser(self):
        """Test argument parser creation."""
        parser = self.cli.create_parser()
        
        # Test that parser is created successfully
        assert parser is not None
        assert parser.prog == 'agent-framework'
        
        # Test that subparsers are added
        # This is a basic test - in practice you'd test specific subcommands
        assert hasattr(parser, '_subparsers')
    
    def test_banner_printing(self, capsys):
        """Test banner printing."""
        self.cli.print_banner()
        
        captured = capsys.readouterr()
        assert "Agent Framework CLI" in captured.out
        assert "Programming Assistant" in captured.out
    
    @pytest.mark.asyncio
    async def test_run_with_help(self):
        """Test running CLI with help argument."""
        # Test that help exits with code 0
        with pytest.raises(SystemExit) as exc_info:
            await self.cli.run(['--help'])
        assert exc_info.value.code == 0


class TestIntegration:
    """Integration tests for CLI components."""
    
    @pytest.mark.asyncio
    async def test_cli_command_flow(self):
        """Test complete CLI command flow."""
        # This would be a more comprehensive integration test
        # that tests the full flow from CLI input to output
        
        # For now, just test that components can be imported and instantiated
        from agent_framework.cli.core import AgentCLI
        from agent_framework.cli.interactive import InteractiveCLI
        from agent_framework.core.config import FrameworkConfig
        
        config = FrameworkConfig()
        cli = AgentCLI()
        
        # Test that CLI can be created without errors
        assert cli is not None
        assert config is not None
    
    def test_plugin_command_integration(self):
        """Test plugin command integration."""
        # Test that all command classes can be imported
        from agent_framework.cli.commands import (
            AnalyzeCommand, GenerateCommand, OptimizeCommand,
            DebugCommand, DocumentCommand
        )
        
        commands = [
            AnalyzeCommand(),
            GenerateCommand(),
            OptimizeCommand(),
            DebugCommand(),
            DocumentCommand()
        ]
        
        # Test that all commands have required properties
        for cmd in commands:
            assert hasattr(cmd, 'name')
            assert hasattr(cmd, 'description')
            assert callable(getattr(cmd, 'execute'))
            assert callable(getattr(cmd, 'add_arguments'))


if __name__ == '__main__':
    pytest.main([__file__])
