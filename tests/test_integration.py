"""
Integration tests for the agent framework.

Tests component interactions and end-to-end workflows.
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, AsyncMock, MagicMock, Mock

from agent_framework.core.orchestrator import AgentOrchestrator
from agent_framework.core.types import Task, TaskPriority, TaskStatus
from agent_framework.core.config import FrameworkConfig, ModelConfig, ExecutionConfig
from agent_framework.execution.executor import TaskExecutor
from agent_framework.plugins.registry import PluginRegistry
from agent_framework.plugins.manager import PluginManager
from agent_framework.monitoring.metrics_collector import MetricsCollector


class TestIntegration:
    """Integration test cases."""
    
    @pytest.mark.asyncio
    async def test_basic_task_execution_flow(self, test_config):
        """Test basic task execution flow through the orchestrator."""
        # Mock external dependencies to avoid actual API calls
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient') as mock_client_class:
            with patch('agent_framework.core.orchestrator.AssistantAgent') as mock_agent_class:
                
                # Setup mock client
                mock_client = AsyncMock()
                mock_client_class.return_value = mock_client
                
                # Setup mock agent
                mock_agent = AsyncMock()
                mock_result = MagicMock()
                mock_result.messages = [MagicMock(content="Task completed successfully")]
                mock_agent.run.return_value = mock_result
                mock_agent_class.return_value = mock_agent
                
                # Create orchestrator
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    # Initialize the orchestrator
                    await orchestrator.initialize()
                    
                    # Verify initialization
                    assert orchestrator.is_initialized
                    assert orchestrator.is_running
                    
                    # Create a test task
                    task = Task(
                        name="Integration Test Task",
                        description="Test task for integration testing",
                        task_type="test",
                        priority=TaskPriority.NORMAL,
                        parameters={"test": True}
                    )
                    
                    # Execute the task
                    result = await orchestrator.execute_task(task)
                    
                    # Verify the result
                    assert result is not None
                    assert hasattr(result, 'status')
                    
                    # Test agent task execution
                    agent_response = await orchestrator.run_agent_task("Test agent task")
                    assert agent_response == "Task completed successfully"
                    
                    # Test metrics collection
                    metrics = await orchestrator.get_metrics()
                    assert metrics is not None
                    assert hasattr(metrics, 'cpu_usage')
                    assert hasattr(metrics, 'memory_usage')
                    
                finally:
                    # Clean shutdown
                    await orchestrator.shutdown()
                    assert not orchestrator.is_initialized
                    assert not orchestrator.is_running
    
    @pytest.mark.asyncio
    async def test_plugin_system_integration(self, test_config, mock_plugin):
        """Test plugin system integration."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Mock plugin loading
                    with patch.object(orchestrator._plugin_manager, 'load_plugin', return_value=mock_plugin):
                        loaded_plugin = await orchestrator.load_plugin("mock_plugin")
                        assert loaded_plugin == mock_plugin
                    
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, test_config):
        """Test error handling across the system."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Test task execution with error
                    task = Task(
                        name="Error Test Task",
                        task_type="test",
                        parameters={"cause_error": True}
                    )
                    
                    # Mock the executor to raise an error
                    with patch.object(orchestrator._task_executor, 'execute_task', side_effect=Exception("Test error")):
                        result = await orchestrator.execute_task(task)
                        
                        # Should handle error gracefully
                        assert result.status.value == "failed"
                        assert "Test error" in result.error
                
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, test_config):
        """Test concurrent task execution."""
        import asyncio
        
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Create multiple tasks
                    tasks = [
                        Task(name=f"Concurrent Task {i}", task_type="test", parameters={"id": i})
                        for i in range(3)
                    ]
                    
                    # Execute tasks concurrently
                    results = await asyncio.gather(*[
                        orchestrator.execute_task(task) for task in tasks
                    ])
                    
                    # Verify all tasks completed
                    assert len(results) == 3
                    for result in results:
                        assert result is not None
                
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_configuration_validation(self):
        """Test configuration validation."""
        from agent_framework.core.config import FrameworkConfig
        
        # Test valid configuration
        config = FrameworkConfig()
        config.model.api_key = "test-key"
        
        errors = config.validate_config()
        assert len(errors) == 0
        
        # Test invalid configuration
        invalid_config = FrameworkConfig()
        # Don't set API key
        
        errors = invalid_config.validate_config()
        assert len(errors) > 0
        assert any("API key" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_system_lifecycle(self, test_config):
        """Test complete system lifecycle."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                # Create orchestrator
                orchestrator = AgentOrchestrator(test_config)
                
                # Initial state
                assert not orchestrator.is_initialized
                assert not orchestrator.is_running
                
                # Initialize
                await orchestrator.initialize()
                assert orchestrator.is_initialized
                assert orchestrator.is_running
                
                # Use the system
                metrics = await orchestrator.get_metrics()
                assert metrics is not None
                
                # Shutdown
                await orchestrator.shutdown()
                assert not orchestrator.is_initialized
                assert not orchestrator.is_running
                
                # Should be able to shutdown again without error
                await orchestrator.shutdown()


class TestComponentIntegration:
    """Test integration between different framework components."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def integration_config(self, temp_dir):
        """Create test configuration for integration tests."""
        return FrameworkConfig(
            model=ModelConfig(
                provider="openai",
                model_name="gpt-3.5-turbo",
                api_key="test-key"
            ),
            execution=ExecutionConfig(
                max_concurrent_tasks=2,
                task_timeout_seconds=30
            ),
            plugin_directories=[temp_dir]
        )

    @pytest.mark.asyncio
    async def test_executor_metrics_integration(self, integration_config):
        """Test integration between task executor and metrics collection."""
        # Create components
        metrics_collector = MetricsCollector(integration_config)
        executor = TaskExecutor(integration_config)

        await executor.initialize()
        metrics_collector.start_collection()

        try:
            # Create a test task
            task = Task(
                name="metrics_integration_task",
                description="Task for testing metrics integration",
                task_type="test",
                priority=TaskPriority.NORMAL
            )

            # Mock task execution to avoid real processing
            with patch.object(executor, '_run_task_logic', new_callable=AsyncMock) as mock_run:
                mock_run.return_value = Mock(
                    status=TaskStatus.COMPLETED,
                    result="Integration test completed",
                    error=None
                )

                # Execute task
                result = await executor.execute_task(task)

                # Verify task completed
                assert result.status == TaskStatus.COMPLETED

                # Record metrics manually (simulating what would happen in real execution)
                metrics_collector.record_counter("integration_tasks_completed", 1.0)
                metrics_collector.record_timer("integration_task_duration", 2.5)
                metrics_collector.record_gauge("integration_active_tasks", 0.0)

                # Verify metrics were recorded
                counter_value = metrics_collector.get_counter_value("integration_tasks_completed")
                assert counter_value == 1.0

                gauge_value = metrics_collector.get_gauge_value("integration_active_tasks")
                assert gauge_value == 0.0

        finally:
            metrics_collector.stop_collection()
            await executor.shutdown()

    @pytest.mark.asyncio
    async def test_plugin_system_integration(self, integration_config, temp_dir):
        """Test integration between plugin registry and manager."""
        # Create a simple test plugin
        plugin_dir = Path(temp_dir) / "integration_plugin"
        plugin_dir.mkdir()

        plugin_file = plugin_dir / "__init__.py"
        plugin_file.write_text('''
class IntegrationPlugin:
    def __init__(self):
        self.name = "integration_plugin"
        self.version = "1.0.0"
        self.description = "Plugin for integration testing"

    def execute(self, data):
        return f"Integration plugin processed: {data}"

    def get_info(self):
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description
        }
''')

        # Initialize plugin system
        registry = PluginRegistry()
        manager = PluginManager(integration_config)

        await manager.initialize()

        try:
            # Discover and load plugins from directory
            await manager.discover_plugins()
            await manager.load_all_plugins()

            # Verify plugin was discovered and loaded
            plugins = registry.get_all_plugins()
            plugin_names = [p.name for p in plugins if hasattr(p, 'name')]

            # Check if our integration plugin was loaded
            integration_plugin = None
            for plugin in plugins:
                if hasattr(plugin, 'name') and plugin.name == "integration_plugin":
                    integration_plugin = plugin
                    break

            if integration_plugin:
                # Test plugin functionality
                result = integration_plugin.execute("test_data")
                assert "Integration plugin processed: test_data" in result

                # Test plugin info
                info = integration_plugin.get_info()
                assert info["name"] == "integration_plugin"
                assert info["version"] == "1.0.0"

        finally:
            await manager.shutdown()

    @pytest.mark.asyncio
    async def test_concurrent_task_execution_integration(self, integration_config):
        """Test concurrent execution of multiple tasks with different priorities."""
        orchestrator = AgentOrchestrator(integration_config)

        with patch('agent_framework.core.model_client_factory.ModelClientFactory.create_client') as mock_client:
            mock_client.return_value = AsyncMock()
            await orchestrator.initialize()

            try:
                # Create tasks with different priorities
                tasks = [
                    Task(
                        name=f"concurrent_task_high_{i}",
                        description=f"High priority concurrent task {i}",
                        task_type="test",
                        priority=TaskPriority.HIGH
                    )
                    for i in range(2)
                ] + [
                    Task(
                        name=f"concurrent_task_normal_{i}",
                        description=f"Normal priority concurrent task {i}",
                        task_type="test",
                        priority=TaskPriority.NORMAL
                    )
                    for i in range(2)
                ]

                # Mock task executor execution
                execution_order = []

                async def mock_execute(task):
                    execution_order.append((task.name, task.priority))
                    await asyncio.sleep(0.1)  # Simulate work
                    return Mock(
                        task_id=task.id,
                        status=TaskStatus.COMPLETED,
                        result=f"Task {task.name} completed",
                        error=None,
                        execution_time=0.1
                    )

                with patch.object(orchestrator._task_executor, 'execute_task', side_effect=mock_execute):
                    # Execute all tasks concurrently
                    results = await asyncio.gather(*[
                        orchestrator.execute_task(task) for task in tasks
                    ])

                    # Verify all tasks completed successfully
                    assert len(results) == 4
                    for result in results:
                        assert result.status == TaskStatus.COMPLETED

                    # Verify all tasks were executed
                    assert len(execution_order) == 4

            finally:
                await orchestrator.shutdown()

    @pytest.mark.asyncio
    async def test_error_propagation_integration(self, integration_config):
        """Test error handling and propagation across components."""
        orchestrator = AgentOrchestrator(integration_config)

        with patch('agent_framework.core.model_client_factory.ModelClientFactory.create_client') as mock_client:
            mock_client.return_value = AsyncMock()
            await orchestrator.initialize()

            try:
                # Create a task that will encounter different types of errors
                error_task = Task(
                    name="error_integration_task",
                    description="Task that will fail for integration testing",
                    task_type="test"
                )

                # Test different error scenarios
                error_scenarios = [
                    ValueError("Validation error"),
                    RuntimeError("Runtime error"),
                    Exception("Generic error")
                ]

                for error in error_scenarios:
                    with patch.object(orchestrator._task_executor, 'execute_task', new_callable=AsyncMock) as mock_execute:
                        mock_execute.side_effect = error

                        # Execute failing task
                        result = await orchestrator.execute_task(error_task)

                        # Verify error was handled properly
                        assert result.status == TaskStatus.FAILED
                        assert result.error is not None
                        assert str(error) in result.error

            finally:
                await orchestrator.shutdown()
