"""
Tests for multi-agent functionality.
"""

import asyncio
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from agent_framework.core.agent_registry import AgentRegistry
from agent_framework.core.agent_manager import AgentManager
from agent_framework.core.multi_agent_types import (
    AgentInfo, AgentStatus, AgentCapability, AgentRoleConfig,
    TaskDelegationRequest, TaskDelegationResponse
)
from agent_framework.core.types import Task, TaskResult, TaskStatus
from agent_framework.core.config import ModelConfig, FrameworkConfig
from agent_framework.agents.base_agent import BaseAgent
from agent_framework.agents.code_analysis_agent import CodeAnalysisAgent
from agent_framework.communication.broker import MessageBroker


class MockAgent(BaseAgent):
    """Mock agent for testing."""
    
    def __init__(self, agent_id=None, capabilities=None):
        self.agent_id = agent_id or uuid4()
        self.capabilities = capabilities or {AgentCapability.CODE_ANALYSIS}
        self._agent_info = AgentInfo(
            id=self.agent_id,
            name="mock_agent",
            capabilities=self.capabilities,
            status=AgentStatus.IDLE
        )
        self._initialized = False
    
    @property
    def agent_info(self):
        return self._agent_info
    
    async def initialize(self, config):
        self._initialized = True
    
    async def execute_task(self, task):
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result={"mock": "result"}
        )
    
    async def handle_message(self, message):
        return None
    
    async def get_capabilities(self):
        return self.capabilities
    
    async def get_status(self):
        return self._agent_info.status
    
    async def shutdown(self):
        self._initialized = False


@pytest_asyncio.fixture
async def agent_registry():
    """Create an agent registry for testing."""
    registry = AgentRegistry()
    yield registry


@pytest_asyncio.fixture
async def message_broker():
    """Create a message broker for testing."""
    config = FrameworkConfig()
    broker = MessageBroker(config)
    yield broker


@pytest_asyncio.fixture
async def agent_manager(agent_registry, message_broker):
    """Create an agent manager for testing."""
    manager = AgentManager(agent_registry, message_broker)
    await manager.start()
    yield manager
    await manager.stop()


class TestAgentRegistry:
    """Test the agent registry functionality."""

    @pytest.mark.asyncio
    async def test_register_agent(self, agent_registry):
        """Test agent registration."""
        agent = MockAgent()
        agent_id = await agent_registry.register_agent(agent)

        assert agent_id == agent.agent_id

        # Verify agent is registered
        registered_agent = await agent_registry.get_agent(agent_id)
        assert registered_agent == agent

        agent_info = await agent_registry.get_agent_info(agent_id)
        assert agent_info.id == agent_id

    @pytest.mark.asyncio
    async def test_unregister_agent(self, agent_registry):
        """Test agent unregistration."""
        agent = MockAgent()
        agent_id = await agent_registry.register_agent(agent)

        # Unregister the agent
        success = await agent_registry.unregister_agent(agent_id)
        assert success

        # Verify agent is no longer registered
        registered_agent = await agent_registry.get_agent(agent_id)
        assert registered_agent is None

    @pytest.mark.asyncio
    async def test_find_agents_by_capability(self, agent_registry):
        """Test finding agents by capability."""
        # Register agents with different capabilities
        agent1 = MockAgent(capabilities={AgentCapability.CODE_ANALYSIS})
        agent2 = MockAgent(capabilities={AgentCapability.TESTING})
        agent3 = MockAgent(capabilities={AgentCapability.CODE_ANALYSIS, AgentCapability.TESTING})

        await agent_registry.register_agent(agent1)
        await agent_registry.register_agent(agent2)
        await agent_registry.register_agent(agent3)

        # Find agents with code analysis capability
        code_agents = await agent_registry.find_agents_by_capability(AgentCapability.CODE_ANALYSIS)
        assert len(code_agents) == 2
        assert agent1.agent_id in code_agents
        assert agent3.agent_id in code_agents

        # Find agents with testing capability
        test_agents = await agent_registry.find_agents_by_capability(AgentCapability.TESTING)
        assert len(test_agents) == 2
        assert agent2.agent_id in test_agents
        assert agent3.agent_id in test_agents

    @pytest.mark.asyncio
    async def test_find_available_agents(self, agent_registry):
        """Test finding available agents."""
        agent1 = MockAgent()
        agent1._agent_info.status = AgentStatus.IDLE

        agent2 = MockAgent()
        agent2._agent_info.status = AgentStatus.BUSY

        await agent_registry.register_agent(agent1)
        await agent_registry.register_agent(agent2)
        
        # Find available agents (exclude busy)
        available = await agent_registry.find_available_agents(exclude_busy=True)
        assert len(available) == 1
        assert agent1.agent_id in available
        
        # Find all agents (include busy)
        all_agents = await agent_registry.find_available_agents(exclude_busy=False)
        assert len(all_agents) == 2


class TestAgentManager:
    """Test the agent manager functionality."""

    @pytest.mark.asyncio
    async def test_delegate_task(self, agent_manager, agent_registry):
        """Test task delegation."""
        # Register a mock agent
        agent = MockAgent()
        await agent_registry.register_agent(agent)

        # Create a task
        task = Task(
            name="test_task",
            description="Test task",
            task_type="test"
        )

        # Create delegation request
        request = TaskDelegationRequest(
            task=task,
            required_capabilities=[AgentCapability.CODE_ANALYSIS]
        )

        # Delegate the task
        response = await agent_manager.delegate_task(request)

        assert response.success
        assert response.assigned_agent_id == agent.agent_id

    @pytest.mark.asyncio
    async def test_execute_task_with_agent(self, agent_manager, agent_registry):
        """Test executing a task with a specific agent."""
        # Register a mock agent
        agent = MockAgent()
        await agent_registry.register_agent(agent)

        # Create a task
        task = Task(
            name="test_task",
            description="Test task",
            task_type="test"
        )

        # Execute the task
        result = await agent_manager.execute_task_with_agent(task, agent.agent_id)

        assert result.status == TaskStatus.COMPLETED
        assert result.task_id == task.id


class TestCodeAnalysisAgent:
    """Test the code analysis agent."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = CodeAnalysisAgent._create_default_config()
        
        assert config.name == "code_analysis_agent"
        assert AgentCapability.CODE_ANALYSIS.value in config.capabilities
        assert AgentCapability.ERROR_DETECTION.value in config.capabilities
    
    @pytest.mark.asyncio
    async def test_analyze_python_complexity(self):
        """Test Python complexity analysis."""
        agent = CodeAnalysisAgent()

        # Simple Python code
        code = """
def simple_function(x):
    if x > 0:
        return x * 2
    else:
        return 0

class SimpleClass:
    def method(self):
        for i in range(10):
            if i % 2 == 0:
                print(i)
"""

        result = await agent._analyze_python_complexity(code)

        assert "cyclomatic_complexity" in result
        assert "cognitive_complexity" in result
        assert "lines_of_code" in result
        assert "function_count" in result
        assert "class_count" in result
        
        assert result["function_count"] == 2  # simple_function + method
        assert result["class_count"] == 1    # SimpleClass
    
    @pytest.mark.asyncio
    async def test_analyze_code_file(self):
        """Test code file analysis."""
        # Mock the role config and MCP manager
        role_config = AgentRoleConfig(
            name="test_agent",
            description="Test agent",
            system_message="Test system message",
            capabilities=[AgentCapability.CODE_ANALYSIS.value]
        )

        agent = CodeAnalysisAgent(role_config)

        # Mock the execute_task method to avoid actual execution
        with patch.object(agent, 'execute_task') as mock_execute:
            mock_execute.return_value = TaskResult(
                task_id=uuid4(),
                status=TaskStatus.COMPLETED,
                result={"analysis": "mock analysis"}
            )

            result = await agent.analyze_code_file("test.py", "comprehensive")

            assert result == {"analysis": "mock analysis"}
            mock_execute.assert_called_once()


class TestMultiAgentIntegration:
    """Integration tests for multi-agent functionality."""

    @pytest.mark.asyncio
    async def test_multi_agent_workflow(self, agent_manager, agent_registry):
        """Test a complete multi-agent workflow."""
        # Register multiple agents with different capabilities
        code_agent = MockAgent(capabilities={AgentCapability.CODE_ANALYSIS})
        test_agent = MockAgent(capabilities={AgentCapability.TESTING})
        doc_agent = MockAgent(capabilities={AgentCapability.DOCUMENTATION})

        await agent_registry.register_agent(code_agent)
        await agent_registry.register_agent(test_agent)
        await agent_registry.register_agent(doc_agent)

        # Create tasks requiring different capabilities
        tasks = [
            Task(name="analyze", description="Analyze code", task_type="analysis"),
            Task(name="test", description="Generate tests", task_type="testing"),
            Task(name="document", description="Create docs", task_type="documentation")
        ]

        # Execute tasks and verify they're assigned to appropriate agents
        for task in tasks:
            if "analyze" in task.name:
                required_caps = [AgentCapability.CODE_ANALYSIS]
                expected_agent = code_agent.agent_id
            elif "test" in task.name:
                required_caps = [AgentCapability.TESTING]
                expected_agent = test_agent.agent_id
            else:
                required_caps = [AgentCapability.DOCUMENTATION]
                expected_agent = doc_agent.agent_id

            request = TaskDelegationRequest(
                task=task,
                required_capabilities=required_caps
            )

            response = await agent_manager.delegate_task(request)
            assert response.success
            assert response.assigned_agent_id == expected_agent

    @pytest.mark.asyncio
    async def test_agent_communication(self, agent_manager, agent_registry):
        """Test inter-agent communication."""
        from agent_framework.core.multi_agent_types import AgentCommunicationMessage

        # Register two agents
        agent1 = MockAgent()
        agent2 = MockAgent()

        await agent_registry.register_agent(agent1)
        await agent_registry.register_agent(agent2)

        # Create a communication message
        message = AgentCommunicationMessage(
            sender_id=agent1.agent_id,
            recipient_id=agent2.agent_id,
            message_type="test_message",
            content={"data": "test"}
        )

        # Send the message
        success = await agent_manager.send_message(message)
        assert success


@pytest.mark.asyncio
async def test_mcp_integration():
    """Test MCP integration functionality."""
    from agent_framework.mcp.types import MCPServerInfo, MCPTransportType
    from agent_framework.mcp.client import MCPClient
    
    # Create a mock MCP server info
    server_info = MCPServerInfo(
        name="test_server",
        command="echo",
        args=["test"],
        transport=MCPTransportType.STDIO
    )
    
    # Create MCP client
    client = MCPClient(server_info)
    
    # Test connection state
    assert not client._is_connected()
    
    # Test metrics
    metrics = client.get_connection_metrics()
    assert metrics.total_requests == 0


if __name__ == "__main__":
    pytest.main([__file__])
