"""
Unit tests for monitoring components.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from agent_framework.monitoring.metrics_collector import MetricsCollector, Metric, MetricType
from agent_framework.monitoring.performance_monitor import PerformanceMonitor
from agent_framework.monitoring.multi_agent_logger import MultiAgentLogger
from agent_framework.core.config import FrameworkConfig
from agent_framework.core.types import Task, TaskResult, TaskStatus


class TestMetricsCollector:
    """Test MetricsCollector functionality."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return FrameworkConfig()

    @pytest.fixture
    def collector(self, config):
        """Create test metrics collector."""
        return MetricsCollector(config)

    def test_initialization(self, collector):
        """Test metrics collector initialization."""
        assert collector.config is not None
        assert collector._metrics is not None
        assert collector._is_collecting is not None

    def test_start_stop_collection(self, collector):
        """Test starting and stopping collection."""
        collector.start_collection()
        collector.stop_collection()

        # Should not raise an exception
        assert True

    def test_record_counter(self, collector):
        """Test recording a counter metric."""
        collector.start_collection()
        collector.record_counter("test_counter", 42.0)

        value = collector.get_counter_value("test_counter")
        assert value == 42.0

    def test_record_gauge(self, collector):
        """Test recording a gauge metric."""
        collector.start_collection()
        collector.record_gauge("test_gauge", 75.5)

        value = collector.get_gauge_value("test_gauge")
        assert value == 75.5

    def test_record_histogram(self, collector):
        """Test recording a histogram metric."""
        collector.start_collection()
        collector.record_histogram("test_histogram", 100.0)

        # Should not raise an exception
        assert True

    def test_record_timer(self, collector):
        """Test recording a timer metric."""
        collector.start_collection()
        collector.record_timer("test_timer", 1.5)

        # Should not raise an exception
        assert True

    def test_get_metric_summary(self, collector):
        """Test getting metric summary."""
        collector.start_collection()
        collector.record_counter("test_metric", 10.0)

        summary = collector.get_metric_summary("test_metric")
        # May return None if metric doesn't exist or no summary available
        assert summary is None or hasattr(summary, 'name')

    def test_get_all_metrics(self, collector):
        """Test getting all metrics."""
        collector.start_collection()
        collector.record_counter("metric1", 10.0)
        collector.record_gauge("metric2", 20.0)

        all_metrics = collector.get_all_metrics()
        assert isinstance(all_metrics, dict)

    def test_reset_metrics(self, collector):
        """Test resetting metrics."""
        collector.start_collection()
        collector.record_counter("test_metric", 10.0)
        collector.reset_metrics()

        # Should not raise an exception
        assert True

    def test_timer_context(self, collector):
        """Test timer context manager."""
        collector.start_collection()
        with collector.start_timer("test_operation"):
            # Simulate some work
            pass

        # Should not raise an exception
        assert True


class TestPerformanceMonitor:
    """Test PerformanceMonitor functionality."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return FrameworkConfig()

    @pytest.fixture
    def monitor(self, config):
        """Create test performance monitor."""
        return PerformanceMonitor(config)

    def test_initialization(self, monitor):
        """Test performance monitor initialization."""
        assert monitor.config is not None
        assert not monitor._is_monitoring
        assert len(monitor._metrics_history) == 0

    @pytest.mark.asyncio
    async def test_start_stop_monitoring(self, monitor):
        """Test starting and stopping monitoring."""
        await monitor.start_monitoring()
        assert monitor._is_monitoring

        await monitor.stop_monitoring()
        assert not monitor._is_monitoring

    def test_get_current_metrics(self, monitor):
        """Test getting current metrics."""
        metrics = monitor.get_current_metrics()
        assert hasattr(metrics, 'timestamp')
        assert hasattr(metrics, 'cpu_usage')
        assert hasattr(metrics, 'memory_usage')

    def test_get_metrics_summary(self, monitor):
        """Test getting metrics summary."""
        summary = monitor.get_metrics_summary(minutes=60)
        assert isinstance(summary, dict)

    def test_add_alert_callback(self, monitor):
        """Test adding alert callback."""
        def test_callback(alert):
            pass

        monitor.add_alert_callback(test_callback)
        # Should not raise an exception
        assert True

    def test_add_custom_metric_collector(self, monitor):
        """Test adding custom metric collector."""
        def test_collector():
            return 42.0

        monitor.add_custom_metric_collector("test_metric", test_collector)
        # Should not raise an exception
        assert True

    def test_set_collection_interval(self, monitor):
        """Test setting collection interval."""
        monitor.set_collection_interval(5.0)
        # Should not raise an exception
        assert True


class TestMultiAgentLogger:
    """Test MultiAgentLogger functionality."""

    @pytest.fixture
    def logger(self):
        """Create test multi-agent logger."""
        return MultiAgentLogger()

    def test_initialization(self, logger):
        """Test logger initialization."""
        assert logger.log_dir is not None
        assert logger._session_id is not None

    def test_get_session_id(self, logger):
        """Test getting session ID."""
        session_id = logger.get_session_id()
        assert isinstance(session_id, str)
        assert len(session_id) > 0

    def test_log_system_event(self, logger):
        """Test logging system event."""
        # This should not raise an exception
        logger.log_system_event("test_event", {"key": "value"})

    def test_log_performance_metric(self, logger):
        """Test logging performance metric."""
        # This should not raise an exception
        logger.log_performance_metric("test_metric", 42.0, "units")

    def test_log_error(self, logger):
        """Test logging error."""
        try:
            raise ValueError("Test error")
        except ValueError as e:
            # This should not raise an exception
            logger.log_error(e, {"context": "test"})

    def test_flush_all_logs(self, logger):
        """Test flushing all logs."""
        # This should not raise an exception
        logger.flush_all_logs()
