"""
Tests for the configuration system.
"""

import json
import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch

from agent_framework.core.config import (
    FrameworkConfig, ModelConfig, CacheConfig, ExecutionConfig,
    PluginConfig, ContextConfig, SecurityConfig, LoggingConfig, MonitoringConfig
)


class TestModelConfig:
    """Test cases for ModelConfig."""

    def test_default_model_config(self):
        """Test default model configuration."""
        config = ModelConfig()

        assert config.model == "qwen/qwen3-coder:free"
        assert config.api_key == ""
        assert config.base_url == "https://openrouter.ai/api/v1"
        assert config.max_tokens == 4096
        assert config.temperature == 0.7
        assert config.timeout_seconds == 30
        assert config.model_info["function_calling"] is True

    def test_custom_model_config(self):
        """Test custom model configuration."""
        config = ModelConfig(
            model="gpt-4",
            api_key="test-key",
            max_tokens=8192,
            temperature=0.5
        )

        assert config.model == "gpt-4"
        assert config.api_key == "test-key"
        assert config.max_tokens == 8192
        assert config.temperature == 0.5


class TestCacheConfig:
    """Test cases for CacheConfig."""

    def test_default_cache_config(self):
        """Test default cache configuration."""
        config = CacheConfig()

        assert config.enabled is True
        assert config.cache_type == "memory"
        assert config.max_memory_size == 1024 * 1024 * 100  # 100MB
        assert config.ttl_seconds == 3600
        assert config.max_entries == 10000

    def test_redis_cache_config(self):
        """Test Redis cache configuration."""
        config = CacheConfig(
            cache_type="redis",
            redis_url="redis://localhost:6379"
        )

        assert config.cache_type == "redis"
        assert config.redis_url == "redis://localhost:6379"


class TestExecutionConfig:
    """Test cases for ExecutionConfig."""

    def test_default_execution_config(self):
        """Test default execution configuration."""
        config = ExecutionConfig()

        assert config.max_concurrent_tasks == 10
        assert config.task_timeout_seconds == 300
        assert config.max_retries == 3
        assert config.retry_delay_seconds == 1.0
        assert config.enable_task_queue is True
        assert config.queue_max_size == 1000

    def test_custom_execution_config(self):
        """Test custom execution configuration."""
        config = ExecutionConfig(
            max_concurrent_tasks=20,
            task_timeout_seconds=600,
            max_retries=5
        )

        assert config.max_concurrent_tasks == 20
        assert config.task_timeout_seconds == 600
        assert config.max_retries == 5


class TestPluginConfig:
    """Test cases for PluginConfig."""

    def test_default_plugin_config(self):
        """Test default plugin configuration."""
        config = PluginConfig()

        assert config.plugin_directories == ["plugins"]
        assert config.auto_load_plugins is True
        assert config.plugin_timeout_seconds == 60
        assert config.max_plugin_memory == 1024 * 1024 * 50  # 50MB
        assert config.sandbox_enabled is True
        assert "os" in config.allowed_imports
        assert "sys" in config.allowed_imports

    def test_custom_plugin_config(self):
        """Test custom plugin configuration."""
        config = PluginConfig(
            plugin_directories=["custom_plugins", "extra_plugins"],
            auto_load_plugins=False,
            sandbox_enabled=False
        )

        assert config.plugin_directories == ["custom_plugins", "extra_plugins"]
        assert config.auto_load_plugins is False
        assert config.sandbox_enabled is False


class TestFrameworkConfig:
    """Test cases for FrameworkConfig."""

    def test_default_framework_config(self):
        """Test default framework configuration."""
        config = FrameworkConfig()

        assert config.name == "Programming Assistant Agent"
        assert config.version == "0.1.0"
        assert config.debug is False

        # Check that all sub-configs are present
        assert isinstance(config.model, ModelConfig)
        assert isinstance(config.cache, CacheConfig)
        assert isinstance(config.execution, ExecutionConfig)
        assert isinstance(config.plugins, PluginConfig)
        assert isinstance(config.context, ContextConfig)
        assert isinstance(config.security, SecurityConfig)
        assert isinstance(config.logging, LoggingConfig)
        assert isinstance(config.monitoring, MonitoringConfig)

    def test_custom_framework_config(self):
        """Test custom framework configuration."""
        config = FrameworkConfig(
            name="Custom Agent",
            version="2.0.0",
            debug=True
        )

        assert config.name == "Custom Agent"
        assert config.version == "2.0.0"
        assert config.debug is True

    def test_config_validation_success(self):
        """Test successful configuration validation."""
        config = FrameworkConfig()
        config.model.api_key = "test-api-key"

        errors = config.validate_config()
        assert len(errors) == 0

    def test_config_validation_missing_api_key(self):
        """Test configuration validation with missing API key."""
        config = FrameworkConfig()
        # Don't set API key

        errors = config.validate_config()
        assert len(errors) > 0
        assert any("API key" in error for error in errors)

    def test_config_validation_redis_without_url(self):
        """Test configuration validation for Redis without URL."""
        config = FrameworkConfig()
        config.model.api_key = "test-key"
        config.cache.cache_type = "redis"
        # Don't set redis_url

        errors = config.validate_config()
        assert len(errors) > 0
        assert any("Redis URL" in error for error in errors)

    def test_config_validation_nonexistent_plugin_dir(self):
        """Test configuration validation with non-existent plugin directory."""
        config = FrameworkConfig()
        config.model.api_key = "test-key"
        config.plugins.plugin_directories = ["/nonexistent/directory"]

        errors = config.validate_config()
        assert len(errors) > 0
        assert any("does not exist" in error for error in errors)

    def test_config_to_file_json(self):
        """Test saving configuration to JSON file."""
        config = FrameworkConfig()
        config.name = "Test Agent"
        config.model.api_key = "test-key"

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_path = f.name

        try:
            config.to_file(config_path)

            # Verify file was created and contains correct data
            assert Path(config_path).exists()

            with open(config_path, 'r') as f:
                data = json.load(f)

            assert data["name"] == "Test Agent"
            assert data["model"]["api_key"] == "test-key"

        finally:
            Path(config_path).unlink(missing_ok=True)

    def test_config_from_file_json(self):
        """Test loading configuration from JSON file."""
        config_data = {
            "name": "Loaded Agent",
            "version": "3.0.0",
            "debug": True,
            "model": {
                "api_key": "loaded-key",
                "model": "gpt-4"
            },
            "execution": {
                "max_concurrent_tasks": 15
            }
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name

        try:
            config = FrameworkConfig.from_file(config_path)

            assert config.name == "Loaded Agent"
            assert config.version == "3.0.0"
            assert config.debug is True
            assert config.model.api_key == "loaded-key"
            assert config.model.model == "gpt-4"
            assert config.execution.max_concurrent_tasks == 15

        finally:
            Path(config_path).unlink(missing_ok=True)

    def test_config_from_file_nonexistent(self):
        """Test loading configuration from non-existent file."""
        with pytest.raises(FileNotFoundError):
            FrameworkConfig.from_file("/nonexistent/config.json")

    def test_config_from_file_unsupported_format(self):
        """Test loading configuration from unsupported file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("not a config file")
            config_path = f.name

        try:
            with pytest.raises(ValueError, match="Unsupported configuration file format"):
                FrameworkConfig.from_file(config_path)
        finally:
            Path(config_path).unlink(missing_ok=True)

    @patch.dict(os.environ, {
        'AGENT_API_KEY': 'env-api-key',
        'AGENT_MODEL': 'env-model',
        'AGENT_BASE_URL': 'https://env.example.com',
        'AGENT_CACHE_TYPE': 'redis',
        'AGENT_REDIS_URL': 'redis://env:6379',
        'AGENT_DEBUG': 'true'
    })
    def test_config_from_env(self):
        """Test loading configuration from environment variables."""
        config = FrameworkConfig.from_env()

        assert config.model.api_key == "env-api-key"
        assert config.model.model == "env-model"
        assert config.model.base_url == "https://env.example.com"
        assert config.cache.cache_type == "redis"
        assert config.cache.redis_url == "redis://env:6379"
        assert config.debug is True

    @patch.dict(os.environ, {
        'AGENT_DEBUG': 'false'
    })
    def test_config_from_env_false_debug(self):
        """Test loading configuration from environment with debug=false."""
        config = FrameworkConfig.from_env()
        assert config.debug is False

    @patch.dict(os.environ, {
        'AGENT_DEBUG': '0'
    })
    def test_config_from_env_debug_zero(self):
        """Test loading configuration from environment with debug=0."""
        config = FrameworkConfig.from_env()
        assert config.debug is False

    def test_config_custom_settings(self):
        """Test custom settings in configuration."""
        config = FrameworkConfig()
        config.custom_settings = {
            "custom_feature": True,
            "custom_value": 42,
            "custom_list": [1, 2, 3]
        }

        assert config.custom_settings["custom_feature"] is True
        assert config.custom_settings["custom_value"] == 42
        assert config.custom_settings["custom_list"] == [1, 2, 3]


class TestOtherConfigs:
    """Test cases for other configuration classes."""

    def test_context_config(self):
        """Test context configuration."""
        config = ContextConfig()

        assert config.context_store_type == "sqlite"
        assert config.max_context_size == 1024 * 1024 * 10  # 10MB
        assert config.context_retention_days == 30
        assert config.enable_semantic_search is True

    def test_security_config(self):
        """Test security configuration."""
        config = SecurityConfig()

        assert config.enable_authentication is False
        assert config.api_key_required is False
        assert "localhost" in config.allowed_hosts
        assert "127.0.0.1" in config.allowed_hosts
        assert config.max_request_size == 1024 * 1024 * 10  # 10MB

    def test_logging_config(self):
        """Test logging configuration."""
        config = LoggingConfig()

        assert config.level == "INFO"
        assert "%(asctime)s" in config.format
        assert config.max_file_size == 1024 * 1024 * 10  # 10MB
        assert config.backup_count == 5
        assert config.enable_structured_logging is True

    def test_monitoring_config(self):
        """Test monitoring configuration."""
        config = MonitoringConfig()

        assert config.enabled is True
        assert config.metrics_endpoint == "/metrics"
        assert config.health_check_endpoint == "/health"
        assert config.collect_performance_metrics is True
        assert config.metrics_retention_days == 7