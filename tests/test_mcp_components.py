"""
Unit tests for MCP (Model Context Protocol) components.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any

from agent_framework.mcp.types import (
    MCPServerInfo, MCPToolCallRequest, MCPToolCallResponse,
    MCPConnectionStatus, MCPTransportType
)
from agent_framework.mcp.server_discovery import MCPServerDiscovery
from agent_framework.core.config import MCPConfig


class TestMCPTypes:
    """Test MCP type definitions."""

    def test_mcp_server_info_creation(self):
        """Test creating MCP server info."""
        server_info = MCPServerInfo(
            name="test_server",
            command="python test_server.py",
            args=["--port", "8080"],
            env={"TEST_VAR": "test_value"}
        )

        assert server_info.name == "test_server"
        assert server_info.command == "python test_server.py"
        assert server_info.args == ["--port", "8080"]
        assert server_info.env == {"TEST_VAR": "test_value"}
        assert server_info.status == MCPConnectionStatus.DISCONNECTED

    def test_mcp_tool_call_request_creation(self):
        """Test creating MCP tool call request."""
        request = MCPToolCallRequest(
            server_name="test_server",
            tool_name="test_tool",
            arguments={"param1": "value1", "param2": 42}
        )

        assert request.server_name == "test_server"
        assert request.tool_name == "test_tool"
        assert request.arguments["param1"] == "value1"
        assert request.arguments["param2"] == 42

    def test_mcp_tool_call_response_creation(self):
        """Test creating MCP tool call response."""
        response = MCPToolCallResponse(
            success=True,
            result="Tool executed successfully",
            server_name="test_server",
            tool_name="test_tool",
            execution_time=1.5
        )

        assert response.success
        assert response.result == "Tool executed successfully"
        assert response.server_name == "test_server"
        assert response.tool_name == "test_tool"
        assert response.execution_time == 1.5

    def test_mcp_tool_call_response_error(self):
        """Test creating MCP tool call response with error."""
        response = MCPToolCallResponse(
            success=False,
            error="Tool execution failed",
            server_name="test_server",
            tool_name="test_tool"
        )

        assert not response.success
        assert response.error == "Tool execution failed"
        assert response.server_name == "test_server"
        assert response.tool_name == "test_tool"


class TestMCPServerDiscovery:
    """Test MCP server discovery."""

    @pytest.fixture
    def discovery(self):
        """Create test server discovery."""
        return MCPServerDiscovery()

    def test_initialization(self, discovery):
        """Test server discovery initialization."""
        assert discovery._discovered_servers is not None
        assert len(discovery._discovered_servers) == 0

    @pytest.mark.asyncio
    async def test_discover_servers(self, discovery):
        """Test discovering servers."""
        # Should not raise an exception
        servers = await discovery.discover_servers()
        assert isinstance(servers, dict)



