"""
Result aggregation system for multi-agent coordination.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field

from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class AggregationRule:
    """Rule for aggregating results from multiple agents."""
    name: str
    condition: Callable[[List[TaskResult]], bool]
    aggregator: Callable[[List[TaskResult]], Any]
    description: str = ""


class ResultAggregator:
    """
    Aggregates results from multiple agents for collaborative tasks.
    """
    
    def __init__(self):
        """Initialize the result aggregator."""
        self.logger = logging.getLogger(__name__)
        
        # Result tracking
        self._pending_aggregations: Dict[str, Dict[str, Any]] = {}
        self._completed_aggregations: Dict[str, Any] = {}
        
        # Aggregation rules
        self._aggregation_rules: Dict[str, AggregationRule] = {}
        
        # Register default aggregation rules
        self._register_default_rules()
    
    def _register_default_rules(self):
        """Register default aggregation rules."""
        # Majority vote rule
        self._aggregation_rules['majority_vote'] = AggregationRule(
            name='majority_vote',
            condition=lambda results: len(results) >= 3,
            aggregator=self._majority_vote_aggregator,
            description='Aggregate based on majority vote of results'
        )
        
        # Best result rule (highest confidence/score)
        self._aggregation_rules['best_result'] = AggregationRule(
            name='best_result',
            condition=lambda results: len(results) >= 1,
            aggregator=self._best_result_aggregator,
            description='Select the result with highest confidence score'
        )
        
        # Consensus rule (all agents agree)
        self._aggregation_rules['consensus'] = AggregationRule(
            name='consensus',
            condition=lambda results: len(results) >= 2,
            aggregator=self._consensus_aggregator,
            description='Aggregate only if all agents agree'
        )
        
        # Average rule (for numeric results)
        self._aggregation_rules['average'] = AggregationRule(
            name='average',
            condition=lambda results: len(results) >= 2,
            aggregator=self._average_aggregator,
            description='Calculate average of numeric results'
        )
    
    async def start_aggregation(self, 
                               aggregation_id: str, 
                               expected_agents: List[str],
                               rule_name: str = 'majority_vote',
                               timeout_seconds: float = 30.0) -> None:
        """
        Start a new result aggregation.
        
        Args:
            aggregation_id: Unique identifier for this aggregation
            expected_agents: List of agent IDs expected to contribute
            rule_name: Name of the aggregation rule to use
            timeout_seconds: Timeout for collecting all results
        """
        if aggregation_id in self._pending_aggregations:
            raise ValueError(f"Aggregation {aggregation_id} already exists")
        
        if rule_name not in self._aggregation_rules:
            raise ValueError(f"Unknown aggregation rule: {rule_name}")
        
        self._pending_aggregations[aggregation_id] = {
            'expected_agents': set(expected_agents),
            'received_results': {},
            'rule_name': rule_name,
            'started_at': datetime.now(),
            'timeout_seconds': timeout_seconds,
            'status': 'pending'
        }
        
        self.logger.info(f"Started aggregation {aggregation_id} with rule {rule_name}")
        
        # Start timeout task
        asyncio.create_task(self._handle_aggregation_timeout(aggregation_id, timeout_seconds))
    
    async def add_result(self, aggregation_id: str, agent_id: str, result: TaskResult) -> Optional[Any]:
        """
        Add a result from an agent to the aggregation.
        
        Returns:
            The aggregated result if aggregation is complete, None otherwise
        """
        if aggregation_id not in self._pending_aggregations:
            self.logger.warning(f"Unknown aggregation: {aggregation_id}")
            return None
        
        aggregation = self._pending_aggregations[aggregation_id]
        
        if aggregation['status'] != 'pending':
            self.logger.warning(f"Aggregation {aggregation_id} is no longer pending")
            return None
        
        if agent_id not in aggregation['expected_agents']:
            self.logger.warning(f"Unexpected agent {agent_id} for aggregation {aggregation_id}")
            return None
        
        # Store the result
        aggregation['received_results'][agent_id] = result
        
        self.logger.debug(f"Added result from {agent_id} to aggregation {aggregation_id}")
        
        # Check if we can aggregate now
        return await self._try_aggregate(aggregation_id)
    
    async def _try_aggregate(self, aggregation_id: str) -> Optional[Any]:
        """Try to aggregate results if conditions are met."""
        aggregation = self._pending_aggregations[aggregation_id]
        results = list(aggregation['received_results'].values())
        rule = self._aggregation_rules[aggregation['rule_name']]
        
        # Check if aggregation condition is met
        if rule.condition(results):
            try:
                # Perform aggregation
                aggregated_result = rule.aggregator(results)
                
                # Mark as completed
                aggregation['status'] = 'completed'
                aggregation['completed_at'] = datetime.now()
                aggregation['result'] = aggregated_result
                
                # Move to completed aggregations
                self._completed_aggregations[aggregation_id] = aggregation
                del self._pending_aggregations[aggregation_id]
                
                self.logger.info(f"Completed aggregation {aggregation_id}")
                return aggregated_result
                
            except Exception as e:
                self.logger.error(f"Error aggregating results for {aggregation_id}: {e}")
                aggregation['status'] = 'failed'
                aggregation['error'] = str(e)
        
        return None
    
    async def _handle_aggregation_timeout(self, aggregation_id: str, timeout_seconds: float):
        """Handle aggregation timeout."""
        await asyncio.sleep(timeout_seconds)
        
        if aggregation_id in self._pending_aggregations:
            aggregation = self._pending_aggregations[aggregation_id]
            
            if aggregation['status'] == 'pending':
                self.logger.warning(f"Aggregation {aggregation_id} timed out")
                
                # Try to aggregate with partial results
                partial_result = await self._try_aggregate(aggregation_id)
                
                if partial_result is None:
                    # Mark as timed out
                    aggregation['status'] = 'timeout'
                    aggregation['completed_at'] = datetime.now()
                    
                    self._completed_aggregations[aggregation_id] = aggregation
                    del self._pending_aggregations[aggregation_id]
    
    def _majority_vote_aggregator(self, results: List[TaskResult]) -> Any:
        """Aggregate results using majority vote."""
        if not results:
            return None
        
        # Count occurrences of each result
        result_counts = {}
        for result in results:
            if result.status == TaskStatus.COMPLETED and result.result:
                key = str(result.result)
                result_counts[key] = result_counts.get(key, 0) + 1
        
        if not result_counts:
            return None
        
        # Return the most common result
        majority_result = max(result_counts, key=result_counts.get)
        return majority_result
    
    def _best_result_aggregator(self, results: List[TaskResult]) -> Any:
        """Select the result with highest confidence score."""
        if not results:
            return None
        
        best_result = None
        best_score = -1
        
        for result in results:
            if result.status == TaskStatus.COMPLETED and result.result:
                # Look for confidence score in metadata
                confidence = 0.5  # Default confidence
                if result.metadata and 'confidence' in result.metadata:
                    confidence = float(result.metadata['confidence'])
                
                if confidence > best_score:
                    best_score = confidence
                    best_result = result.result
        
        return best_result
    
    def _consensus_aggregator(self, results: List[TaskResult]) -> Any:
        """Aggregate only if all agents agree."""
        if not results:
            return None
        
        # Check if all results are the same
        first_result = None
        for result in results:
            if result.status == TaskStatus.COMPLETED and result.result:
                if first_result is None:
                    first_result = result.result
                elif str(result.result) != str(first_result):
                    return None  # No consensus
        
        return first_result
    
    def _average_aggregator(self, results: List[TaskResult]) -> Any:
        """Calculate average of numeric results."""
        if not results:
            return None
        
        numeric_results = []
        for result in results:
            if result.status == TaskStatus.COMPLETED and result.result:
                try:
                    numeric_results.append(float(result.result))
                except (ValueError, TypeError):
                    continue
        
        if not numeric_results:
            return None
        
        return sum(numeric_results) / len(numeric_results)
    
    def get_aggregation_status(self, aggregation_id: str) -> Optional[Dict[str, Any]]:
        """Get status of an aggregation."""
        if aggregation_id in self._pending_aggregations:
            return dict(self._pending_aggregations[aggregation_id])
        elif aggregation_id in self._completed_aggregations:
            return dict(self._completed_aggregations[aggregation_id])
        else:
            return None
    
    def add_aggregation_rule(self, rule: AggregationRule):
        """Add a custom aggregation rule."""
        self._aggregation_rules[rule.name] = rule
        self.logger.info(f"Added aggregation rule: {rule.name}")
