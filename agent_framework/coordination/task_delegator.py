"""
Task delegation system for multi-agent coordination.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.types import Task, TaskResult, TaskStatus, TaskPriority
from ..core.config import FrameworkConfig


class TaskDelegator:
    """
    Handles task delegation to appropriate agents based on capabilities and load.
    """
    
    def __init__(self, config: FrameworkConfig):
        """Initialize the task delegator."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Agent tracking
        self._available_agents: Dict[str, Any] = {}
        self._agent_capabilities: Dict[str, List[str]] = {}
        self._agent_load: Dict[str, int] = {}
        
        # Task tracking
        self._delegated_tasks: Dict[str, str] = {}  # task_id -> agent_id
        self._task_history: List[Dict[str, Any]] = []
    
    async def delegate_task(self, task: Task, available_agents: List[str] = None) -> Optional[str]:
        """
        Delegate a task to the most suitable agent.
        
        Args:
            task: The task to delegate
            available_agents: Optional list of specific agents to consider
            
        Returns:
            The ID of the agent the task was delegated to, or None if no suitable agent found
        """
        try:
            # Find the best agent for this task
            best_agent = await self._find_best_agent(task, available_agents)
            
            if not best_agent:
                self.logger.warning(f"No suitable agent found for task: {task.name}")
                return None
            
            # Record the delegation
            self._delegated_tasks[str(task.id)] = best_agent
            self._agent_load[best_agent] = self._agent_load.get(best_agent, 0) + 1
            
            # Log the delegation
            self.logger.info(f"Delegated task {task.name} to agent {best_agent}")
            
            # Record in history
            self._task_history.append({
                'task_id': str(task.id),
                'task_name': task.name,
                'agent_id': best_agent,
                'delegated_at': datetime.now(),
                'task_type': task.task_type,
                'priority': task.priority.name
            })
            
            return best_agent
            
        except Exception as e:
            self.logger.error(f"Error delegating task {task.name}: {e}")
            return None
    
    async def _find_best_agent(self, task: Task, available_agents: List[str] = None) -> Optional[str]:
        """Find the best agent for a given task."""
        candidate_agents = available_agents or list(self._available_agents.keys())
        
        if not candidate_agents:
            return None
        
        # Score each agent
        agent_scores = {}
        
        for agent_id in candidate_agents:
            score = await self._calculate_agent_score(agent_id, task)
            if score > 0:
                agent_scores[agent_id] = score
        
        if not agent_scores:
            return None
        
        # Return the agent with the highest score
        return max(agent_scores, key=agent_scores.get)
    
    async def _calculate_agent_score(self, agent_id: str, task: Task) -> float:
        """Calculate how suitable an agent is for a task."""
        score = 0.0
        
        # Check if agent has required capabilities
        agent_capabilities = self._agent_capabilities.get(agent_id, [])
        
        # Base score for having any capabilities
        if agent_capabilities:
            score += 1.0
        
        # Bonus for specific task type capability
        if task.task_type in agent_capabilities:
            score += 5.0
        
        # Penalty for high load
        agent_load = self._agent_load.get(agent_id, 0)
        max_load = 10  # Configurable threshold
        
        if agent_load < max_load:
            load_penalty = agent_load / max_load
            score *= (1.0 - load_penalty * 0.5)  # Up to 50% penalty for load
        else:
            score = 0.0  # Agent is overloaded
        
        # Priority bonus
        if task.priority == TaskPriority.CRITICAL:
            score *= 1.5
        elif task.priority == TaskPriority.HIGH:
            score *= 1.2
        
        return score
    
    def register_agent(self, agent_id: str, capabilities: List[str], agent_instance: Any = None):
        """Register an agent with its capabilities."""
        self._available_agents[agent_id] = agent_instance
        self._agent_capabilities[agent_id] = capabilities
        self._agent_load[agent_id] = 0
        
        self.logger.info(f"Registered agent {agent_id} with capabilities: {capabilities}")
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent."""
        if agent_id in self._available_agents:
            del self._available_agents[agent_id]
            del self._agent_capabilities[agent_id]
            del self._agent_load[agent_id]
            
            self.logger.info(f"Unregistered agent {agent_id}")
    
    def task_completed(self, task_id: str):
        """Mark a task as completed and update agent load."""
        if task_id in self._delegated_tasks:
            agent_id = self._delegated_tasks[task_id]
            if agent_id in self._agent_load:
                self._agent_load[agent_id] = max(0, self._agent_load[agent_id] - 1)
            
            del self._delegated_tasks[task_id]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current status of all agents."""
        return {
            'available_agents': list(self._available_agents.keys()),
            'agent_capabilities': dict(self._agent_capabilities),
            'agent_load': dict(self._agent_load),
            'active_delegations': len(self._delegated_tasks)
        }
    
    def get_delegation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent delegation history."""
        return self._task_history[-limit:] if self._task_history else []
