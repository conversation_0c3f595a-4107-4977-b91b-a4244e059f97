"""
Enhanced code editing workflow with validation and error prevention.
"""

import ast
import logging
import difflib
from typing import Dict, List, Any, Optional, <PERSON><PERSON>
from dataclasses import dataclass
from pathlib import Path
import tempfile
import subprocess

from .code_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON>, CodeContext
from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class EditValidation:
    """Represents validation results for a code edit."""
    is_valid: bool
    syntax_errors: List[Dict[str, Any]]
    semantic_errors: List[Dict[str, Any]]
    warnings: List[Dict[str, Any]]
    compatibility_issues: List[Dict[str, Any]]
    suggestions: List[str]


@dataclass
class CodeEdit:
    """Represents a code edit operation."""
    file_path: str
    original_content: str
    modified_content: str
    edit_type: str  # 'insert', 'replace', 'delete', 'refactor'
    line_range: Tuple[int, int]
    description: str
    validation: Optional[EditValidation] = None


class CodeEditor:
    """Advanced code editor with validation and compatibility checking."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.analyzer = CodeAnalyzer()
        self._edit_history: List[CodeEdit] = []
        
    async def prepare_edit(self, 
                          file_path: str,
                          original_content: str,
                          edit_description: str,
                          context_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Prepare for a code edit by analyzing context and dependencies.
        
        Args:
            file_path: Path to the file being edited
            original_content: Current content of the file
            edit_description: Description of the intended edit
            context_info: Additional context information
            
        Returns:
            Preparation results with recommendations
        """
        try:
            # Analyze current code context
            current_context = await self.analyzer.analyze_code_comprehensive(
                original_content, file_path
            )
            
            # Identify dependencies and relationships
            dependencies = self._identify_edit_dependencies(current_context, edit_description)
            
            # Check for potential conflicts
            conflicts = self._check_potential_conflicts(current_context, edit_description)
            
            # Generate edit recommendations
            recommendations = self._generate_edit_recommendations(
                current_context, edit_description, dependencies
            )
            
            return {
                "context": current_context,
                "dependencies": dependencies,
                "potential_conflicts": conflicts,
                "recommendations": recommendations,
                "safety_score": self._calculate_safety_score(current_context, conflicts)
            }
            
        except Exception as e:
            self.logger.error(f"Edit preparation failed: {e}")
            return {
                "error": str(e),
                "safety_score": 0.0,
                "recommendations": ["Manual review required due to analysis error"]
            }
    
    async def validate_edit(self, edit: CodeEdit) -> EditValidation:
        """
        Validate a code edit before applying it.
        
        Args:
            edit: The code edit to validate
            
        Returns:
            Validation results
        """
        syntax_errors = []
        semantic_errors = []
        warnings = []
        compatibility_issues = []
        suggestions = []
        
        try:
            # Check syntax validity
            try:
                ast.parse(edit.modified_content)
            except SyntaxError as e:
                syntax_errors.append({
                    "type": "SyntaxError",
                    "message": str(e),
                    "line": e.lineno,
                    "column": e.offset
                })
            
            # Analyze modified code
            modified_context = await self.analyzer.analyze_code_comprehensive(
                edit.modified_content, edit.file_path
            )
            
            # Check for semantic issues
            semantic_errors.extend(self._check_semantic_issues(modified_context))
            
            # Check compatibility with existing codebase
            compatibility_issues.extend(
                await self._check_compatibility(edit, modified_context)
            )
            
            # Generate warnings for potential issues
            warnings.extend(self._generate_warnings(modified_context))
            
            # Generate improvement suggestions
            suggestions.extend(self._generate_improvement_suggestions(modified_context))
            
            is_valid = len(syntax_errors) == 0 and len(semantic_errors) == 0
            
        except Exception as e:
            self.logger.error(f"Edit validation failed: {e}")
            semantic_errors.append({
                "type": "ValidationError",
                "message": f"Validation failed: {str(e)}"
            })
            is_valid = False
        
        return EditValidation(
            is_valid=is_valid,
            syntax_errors=syntax_errors,
            semantic_errors=semantic_errors,
            warnings=warnings,
            compatibility_issues=compatibility_issues,
            suggestions=suggestions
        )
    
    async def apply_edit_safely(self, edit: CodeEdit) -> Dict[str, Any]:
        """
        Apply a code edit with safety checks and rollback capability.
        
        Args:
            edit: The code edit to apply
            
        Returns:
            Application results
        """
        try:
            # Validate edit first
            validation = await self.validate_edit(edit)
            edit.validation = validation
            
            if not validation.is_valid:
                return {
                    "success": False,
                    "error": "Edit validation failed",
                    "validation": validation
                }
            
            # Create backup
            backup_content = edit.original_content
            
            # Apply the edit
            result = self._apply_edit_operation(edit)
            
            if result["success"]:
                # Add to history
                self._edit_history.append(edit)
                
                # Verify the edit was applied correctly
                verification = await self._verify_edit_application(edit)
                
                if not verification["success"]:
                    # Rollback if verification failed
                    rollback_result = self._rollback_edit(edit, backup_content)
                    return {
                        "success": False,
                        "error": "Edit verification failed, rolled back",
                        "verification": verification,
                        "rollback": rollback_result
                    }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Edit application failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_diff(self, original: str, modified: str) -> str:
        """Generate a unified diff between original and modified content."""
        original_lines = original.splitlines(keepends=True)
        modified_lines = modified.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile="original",
            tofile="modified",
            lineterm=""
        )
        
        return ''.join(diff)
    
    def _identify_edit_dependencies(self, context: CodeContext, edit_description: str) -> List[str]:
        """Identify dependencies that might be affected by the edit."""
        dependencies = []
        
        # Check if edit affects imports
        if "import" in edit_description.lower():
            dependencies.extend(context.imports)
        
        # Check if edit affects classes
        for class_info in context.classes:
            if class_info["name"].lower() in edit_description.lower():
                dependencies.append(f"class:{class_info['name']}")
        
        # Check if edit affects functions
        for func_info in context.functions:
            if func_info["name"].lower() in edit_description.lower():
                dependencies.append(f"function:{func_info['name']}")
        
        return dependencies
    
    def _check_potential_conflicts(self, context: CodeContext, edit_description: str) -> List[Dict[str, Any]]:
        """Check for potential conflicts with the edit."""
        conflicts = []
        
        # Check for naming conflicts
        if "def " in edit_description or "class " in edit_description:
            # Extract potential new names
            import re
            names = re.findall(r'(?:def|class)\s+(\w+)', edit_description)
            
            for name in names:
                # Check if name already exists
                existing_functions = [f["name"] for f in context.functions]
                existing_classes = [c["name"] for c in context.classes]
                
                if name in existing_functions:
                    conflicts.append({
                        "type": "naming_conflict",
                        "message": f"Function '{name}' already exists",
                        "severity": "high"
                    })
                
                if name in existing_classes:
                    conflicts.append({
                        "type": "naming_conflict",
                        "message": f"Class '{name}' already exists",
                        "severity": "high"
                    })
        
        return conflicts
    
    def _generate_edit_recommendations(self, 
                                     context: CodeContext, 
                                     edit_description: str,
                                     dependencies: List[str]) -> List[str]:
        """Generate recommendations for the edit."""
        recommendations = []
        
        # Recommend testing if adding new functionality
        if any(keyword in edit_description.lower() for keyword in ["add", "new", "create"]):
            recommendations.append("Consider adding unit tests for new functionality")
        
        # Recommend documentation updates
        if "class" in edit_description.lower() or "def" in edit_description.lower():
            recommendations.append("Update documentation and docstrings")
        
        # Recommend dependency updates
        if dependencies:
            recommendations.append(f"Review dependencies: {', '.join(dependencies)}")
        
        # Check complexity
        if context.complexity_metrics.get("cyclomatic_complexity", 0) > 10:
            recommendations.append("Consider refactoring to reduce complexity")
        
        return recommendations
    
    def _calculate_safety_score(self, context: CodeContext, conflicts: List[Dict]) -> float:
        """Calculate a safety score for the edit (0.0 to 1.0)."""
        score = 1.0
        
        # Reduce score for conflicts
        score -= len(conflicts) * 0.2
        
        # Reduce score for high complexity
        complexity = context.complexity_metrics.get("cyclomatic_complexity", 0)
        if complexity > 15:
            score -= 0.3
        elif complexity > 10:
            score -= 0.1
        
        # Reduce score for potential issues
        score -= len(context.potential_issues) * 0.1
        
        return max(0.0, min(1.0, score))
    
    def _check_semantic_issues(self, context: CodeContext) -> List[Dict[str, Any]]:
        """Check for semantic issues in the code."""
        issues = []
        
        # Check for undefined variables (simplified)
        defined_vars = {var["name"] for var in context.variables}
        
        # Check for unused imports (simplified)
        used_imports = set()
        for func in context.functions:
            for call in func.get("calls", []):
                if "." in call:
                    used_imports.add(call.split(".")[0])
        
        unused_imports = set(imp.split(".")[0] for imp in context.imports) - used_imports
        for unused in unused_imports:
            issues.append({
                "type": "unused_import",
                "message": f"Import '{unused}' appears to be unused",
                "severity": "low"
            })
        
        return issues
    
    async def _check_compatibility(self, edit: CodeEdit, context: CodeContext) -> List[Dict[str, Any]]:
        """Check compatibility with existing codebase."""
        issues = []
        
        # This would typically involve checking against other files in the project
        # For now, we'll do basic checks
        
        # Check for breaking changes to public APIs
        if edit.edit_type == "replace":
            # Check if we're modifying function signatures
            original_functions = []
            modified_functions = []
            
            try:
                original_tree = ast.parse(edit.original_content)
                for node in ast.walk(original_tree):
                    if isinstance(node, ast.FunctionDef):
                        original_functions.append({
                            "name": node.name,
                            "args": [arg.arg for arg in node.args.args]
                        })
            except:
                pass
            
            for func in context.functions:
                modified_functions.append({
                    "name": func["name"],
                    "args": func["args"]
                })
            
            # Check for signature changes
            for orig_func in original_functions:
                for mod_func in modified_functions:
                    if orig_func["name"] == mod_func["name"]:
                        if orig_func["args"] != mod_func["args"]:
                            issues.append({
                                "type": "breaking_change",
                                "message": f"Function '{orig_func['name']}' signature changed",
                                "severity": "high"
                            })
        
        return issues
    
    def _generate_warnings(self, context: CodeContext) -> List[Dict[str, Any]]:
        """Generate warnings for potential issues."""
        warnings = []
        
        # Warn about high complexity
        complexity = context.complexity_metrics.get("cyclomatic_complexity", 0)
        if complexity > 15:
            warnings.append({
                "type": "high_complexity",
                "message": f"Code has high cyclomatic complexity ({complexity})",
                "severity": "medium"
            })
        
        # Warn about long functions
        for func in context.functions:
            if func.get("complexity", 0) > 10:
                warnings.append({
                    "type": "complex_function",
                    "message": f"Function '{func['name']}' has high complexity",
                    "severity": "medium"
                })
        
        return warnings
    
    def _generate_improvement_suggestions(self, context: CodeContext) -> List[str]:
        """Generate suggestions for code improvement."""
        suggestions = []
        
        # Suggest adding docstrings
        functions_without_docs = [f for f in context.functions if not f.get("docstring")]
        if functions_without_docs:
            suggestions.append("Consider adding docstrings to functions without documentation")
        
        # Suggest type hints
        functions_without_types = [f for f in context.functions if not f.get("return_annotation")]
        if functions_without_types:
            suggestions.append("Consider adding type hints for better code clarity")
        
        # Suggest breaking down complex functions
        complex_functions = [f for f in context.functions if f.get("complexity", 0) > 8]
        if complex_functions:
            suggestions.append("Consider breaking down complex functions into smaller ones")
        
        return suggestions
    
    def _apply_edit_operation(self, edit: CodeEdit) -> Dict[str, Any]:
        """Apply the actual edit operation."""
        try:
            # For now, this is a placeholder
            # In a real implementation, this would write to the file system
            return {
                "success": True,
                "message": "Edit applied successfully",
                "diff": self.generate_diff(edit.original_content, edit.modified_content)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _verify_edit_application(self, edit: CodeEdit) -> Dict[str, Any]:
        """Verify that the edit was applied correctly."""
        try:
            # Verify syntax is still valid
            ast.parse(edit.modified_content)
            
            # Verify the edit meets expectations
            # This could include running tests, checking imports, etc.
            
            return {
                "success": True,
                "message": "Edit verification passed"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Edit verification failed: {str(e)}"
            }
    
    def _rollback_edit(self, edit: CodeEdit, backup_content: str) -> Dict[str, Any]:
        """Rollback an edit to the backup content."""
        try:
            # Restore the backup content
            # In a real implementation, this would write to the file system
            return {
                "success": True,
                "message": "Edit rolled back successfully"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Rollback failed: {str(e)}"
            }
    
    def get_edit_history(self) -> List[CodeEdit]:
        """Get the history of edits."""
        return self._edit_history.copy()
