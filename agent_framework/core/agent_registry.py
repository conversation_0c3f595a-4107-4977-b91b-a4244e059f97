"""
Agent registry for managing multiple agents in the framework.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Set
from uuid import UUID

from .multi_agent_types import (
    AgentInfo, AgentStatus, AgentCapability, AgentInterface,
    AgentMetrics, CoordinationStrategy
)


class AgentRegistry:
    """
    Registry for managing agents in the multi-agent system.
    
    Provides centralized management of agent lifecycle, status tracking,
    and capability-based agent discovery.
    """
    
    def __init__(self):
        """Initialize the agent registry."""
        self.logger = logging.getLogger(__name__)
        self._agents: Dict[UUID, AgentInfo] = {}
        self._agent_instances: Dict[UUID, AgentInterface] = {}
        self._capability_index: Dict[AgentCapability, Set[UUID]] = {}
        self._role_index: Dict[str, Set[UUID]] = {}
        self._lock = asyncio.Lock()
    
    async def register_agent(self, agent: AgentInterface) -> UUID:
        """
        Register a new agent in the registry.
        
        Args:
            agent: The agent instance to register
            
        Returns:
            The UUID of the registered agent
        """
        async with self._lock:
            agent_info = agent.agent_info
            agent_id = agent_info.id
            
            self.logger.info(f"Registering agent: {agent_info.name} ({agent_id})")
            
            # Store agent info and instance
            self._agents[agent_id] = agent_info
            self._agent_instances[agent_id] = agent
            
            # Update capability index
            capabilities = await agent.get_capabilities()
            for capability in capabilities:
                if capability not in self._capability_index:
                    self._capability_index[capability] = set()
                self._capability_index[capability].add(agent_id)
            
            # Update role index
            if agent_info.role:
                if agent_info.role not in self._role_index:
                    self._role_index[agent_info.role] = set()
                self._role_index[agent_info.role].add(agent_id)
            
            self.logger.info(f"Agent {agent_info.name} registered successfully")
            return agent_id
    
    async def unregister_agent(self, agent_id: UUID) -> bool:
        """
        Unregister an agent from the registry.
        
        Args:
            agent_id: The UUID of the agent to unregister
            
        Returns:
            True if the agent was successfully unregistered
        """
        async with self._lock:
            if agent_id not in self._agents:
                self.logger.warning(f"Attempted to unregister unknown agent: {agent_id}")
                return False
            
            agent_info = self._agents[agent_id]
            self.logger.info(f"Unregistering agent: {agent_info.name} ({agent_id})")
            
            # Remove from capability index
            agent_instance = self._agent_instances.get(agent_id)
            if agent_instance:
                capabilities = await agent_instance.get_capabilities()
                for capability in capabilities:
                    if capability in self._capability_index:
                        self._capability_index[capability].discard(agent_id)
                        if not self._capability_index[capability]:
                            del self._capability_index[capability]
            
            # Remove from role index
            if agent_info.role and agent_info.role in self._role_index:
                self._role_index[agent_info.role].discard(agent_id)
                if not self._role_index[agent_info.role]:
                    del self._role_index[agent_info.role]
            
            # Remove agent
            del self._agents[agent_id]
            if agent_id in self._agent_instances:
                del self._agent_instances[agent_id]
            
            self.logger.info(f"Agent {agent_info.name} unregistered successfully")
            return True
    
    async def get_agent(self, agent_id: UUID) -> Optional[AgentInterface]:
        """
        Get an agent instance by ID.
        
        Args:
            agent_id: The UUID of the agent
            
        Returns:
            The agent instance or None if not found
        """
        return self._agent_instances.get(agent_id)
    
    async def get_agent_info(self, agent_id: UUID) -> Optional[AgentInfo]:
        """
        Get agent information by ID.
        
        Args:
            agent_id: The UUID of the agent
            
        Returns:
            The agent information or None if not found
        """
        return self._agents.get(agent_id)
    
    async def find_agents_by_capability(self, capability: AgentCapability) -> List[UUID]:
        """
        Find agents that have a specific capability.
        
        Args:
            capability: The capability to search for
            
        Returns:
            List of agent UUIDs that have the capability
        """
        return list(self._capability_index.get(capability, set()))
    
    async def find_agents_by_role(self, role: str) -> List[UUID]:
        """
        Find agents that have a specific role.
        
        Args:
            role: The role to search for
            
        Returns:
            List of agent UUIDs that have the role
        """
        return list(self._role_index.get(role, set()))
    
    async def find_available_agents(self, 
                                   capabilities: Optional[List[AgentCapability]] = None,
                                   role: Optional[str] = None,
                                   exclude_busy: bool = True) -> List[UUID]:
        """
        Find available agents based on criteria.
        
        Args:
            capabilities: Required capabilities (all must be present)
            role: Required role
            exclude_busy: Whether to exclude busy agents
            
        Returns:
            List of agent UUIDs that match the criteria
        """
        candidate_agents = set(self._agents.keys())
        
        # Filter by capabilities
        if capabilities:
            for capability in capabilities:
                capability_agents = set(self._capability_index.get(capability, set()))
                candidate_agents &= capability_agents
        
        # Filter by role
        if role:
            role_agents = set(self._role_index.get(role, set()))
            candidate_agents &= role_agents
        
        # Filter by status
        if exclude_busy:
            available_agents = []
            for agent_id in candidate_agents:
                agent_info = self._agents.get(agent_id)
                if agent_info and agent_info.status in [AgentStatus.IDLE]:
                    available_agents.append(agent_id)
            return available_agents
        
        return list(candidate_agents)
    
    async def update_agent_status(self, agent_id: UUID, status: AgentStatus) -> bool:
        """
        Update the status of an agent.
        
        Args:
            agent_id: The UUID of the agent
            status: The new status
            
        Returns:
            True if the status was updated successfully
        """
        async with self._lock:
            if agent_id not in self._agents:
                return False
            
            self._agents[agent_id].status = status
            self.logger.debug(f"Updated agent {agent_id} status to {status.value}")
            return True
    
    async def update_agent_metrics(self, agent_id: UUID, metrics: AgentMetrics) -> bool:
        """
        Update the metrics of an agent.
        
        Args:
            agent_id: The UUID of the agent
            metrics: The new metrics
            
        Returns:
            True if the metrics were updated successfully
        """
        async with self._lock:
            if agent_id not in self._agents:
                return False
            
            self._agents[agent_id].metrics = metrics
            return True
    
    async def get_all_agents(self) -> Dict[UUID, AgentInfo]:
        """
        Get information about all registered agents.
        
        Returns:
            Dictionary mapping agent UUIDs to agent information
        """
        return self._agents.copy()
    
    async def get_registry_stats(self) -> Dict[str, any]:
        """
        Get statistics about the agent registry.
        
        Returns:
            Dictionary containing registry statistics
        """
        total_agents = len(self._agents)
        status_counts = {}
        capability_counts = {}
        role_counts = {}
        
        for agent_info in self._agents.values():
            # Count by status
            status = agent_info.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # Count by role
            if agent_info.role:
                role_counts[agent_info.role] = role_counts.get(agent_info.role, 0) + 1
        
        # Count by capability
        for capability, agent_ids in self._capability_index.items():
            capability_counts[capability.value] = len(agent_ids)
        
        return {
            "total_agents": total_agents,
            "status_distribution": status_counts,
            "capability_distribution": capability_counts,
            "role_distribution": role_counts,
            "total_capabilities": len(self._capability_index),
            "total_roles": len(self._role_index)
        }
