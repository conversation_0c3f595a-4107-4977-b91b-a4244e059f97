"""
Core types and data structures for the agent framework.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel


class TaskStatus(Enum):
    """Status of a task in the execution pipeline."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Priority levels for task execution."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Task:
    """Represents a task to be executed by the agent framework."""
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    description: str = ""
    task_type: str = "generic"
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[UUID] = field(default_factory=list)
    timeout_seconds: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3

    def __lt__(self, other):
        """Compare tasks by priority for queue ordering."""
        if not isinstance(other, Task):
            return NotImplemented
        # Higher priority values should come first (reverse order)
        return self.priority.value > other.priority.value

    def __eq__(self, other):
        """Compare tasks by ID."""
        if not isinstance(other, Task):
            return NotImplemented
        return self.id == other.id

    def __hash__(self):
        """Hash based on task ID."""
        return hash(self.id)


@dataclass
class TaskResult:
    """Result of task execution."""
    task_id: UUID
    status: TaskStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


class PluginCapability(BaseModel):
    """Describes a capability provided by a plugin."""
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    supported_languages: List[str] = []
    performance_metrics: Dict[str, Any] = {}


class PluginRequest(BaseModel):
    """Request sent to a plugin for execution."""
    capability: str
    parameters: Dict[str, Any]
    context: Dict[str, Any] = {}
    timeout_seconds: Optional[int] = None


class PluginResponse(BaseModel):
    """Response from a plugin execution."""
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = {}


class ContextQuery(BaseModel):
    """Query for retrieving context information."""
    query_type: str
    parameters: Dict[str, Any]
    scope: str = "global"
    max_results: int = 100


class Context(BaseModel):
    """Context information retrieved from the context management system."""
    query_id: str
    results: List[Dict[str, Any]]
    metadata: Dict[str, Any] = {}
    retrieved_at: datetime = field(default_factory=datetime.now)


class AgentEvent(BaseModel):
    """Event emitted by the agent framework."""
    event_type: str
    source: str
    data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)


class ResourceMetrics(BaseModel):
    """System resource usage metrics."""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_usage: float
    active_tasks: int
    plugin_count: int
    timestamp: datetime = field(default_factory=datetime.now)


class PluginInterface(ABC):
    """Abstract base class for all plugins."""

    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        pass

    @abstractmethod
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        pass

    @abstractmethod
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """Get the plugin name."""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """Get the plugin version."""
        pass