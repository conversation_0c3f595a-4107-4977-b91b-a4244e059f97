"""
Multi-agent types and data structures for the agent framework.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
from uuid import UUID, uuid4

from pydantic import BaseModel

from .types import Task, TaskResult, TaskStatus
from .config import ModelConfig, AgentRoleConfig


class AgentStatus(Enum):
    """Status of an agent in the multi-agent system."""
    IDLE = "idle"
    BUSY = "busy"
    OFFLINE = "offline"
    ERROR = "error"
    INITIALIZING = "initializing"


class AgentCapability(Enum):
    """Capabilities that agents can have."""
    CODE_ANALYSIS = "code_analysis"
    CODE_GENERATION = "code_generation"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    REFACTORING = "refactoring"
    ERROR_DETECTION = "error_detection"
    OPTIMIZATION = "optimization"
    FILE_OPERATIONS = "file_operations"
    WEB_SEARCH = "web_search"
    DATABASE_OPERATIONS = "database_operations"


class CoordinationStrategy(Enum):
    """Strategies for coordinating multiple agents."""
    ROUND_ROBIN = "round_robin"
    PRIORITY = "priority"
    LOAD_BALANCED = "load_balanced"
    CAPABILITY_BASED = "capability_based"


class ConflictResolutionStrategy(Enum):
    """Strategies for resolving conflicts between agents."""
    VOTING = "voting"
    PRIORITY = "priority"
    CONSENSUS = "consensus"
    FIRST_WINS = "first_wins"


@dataclass
class AgentMetrics:
    """Metrics for tracking agent performance."""
    tasks_completed: int = 0
    tasks_failed: int = 0
    average_execution_time: float = 0.0
    total_execution_time: float = 0.0
    last_activity: Optional[datetime] = None
    error_count: int = 0
    success_rate: float = 0.0


@dataclass
class AgentInfo:
    """Information about an agent in the multi-agent system."""
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    role: str = ""
    status: AgentStatus = AgentStatus.OFFLINE
    capabilities: Set[AgentCapability] = field(default_factory=set)
    model_config: Optional[ModelConfig] = None
    mcp_servers: List[str] = field(default_factory=list)
    current_task: Optional[UUID] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_seen: datetime = field(default_factory=datetime.now)
    metrics: AgentMetrics = field(default_factory=AgentMetrics)
    priority: int = 1
    max_concurrent_tasks: int = 1


class TaskDelegationRequest(BaseModel):
    """Request to delegate a task to an agent."""
    task: Task
    preferred_agent_id: Optional[UUID] = None
    required_capabilities: List[AgentCapability] = []
    priority: int = 1
    timeout_seconds: Optional[int] = None


class TaskDelegationResponse(BaseModel):
    """Response to a task delegation request."""
    success: bool
    assigned_agent_id: Optional[UUID] = None
    estimated_completion_time: Optional[float] = None
    error_message: Optional[str] = None


class AgentCommunicationMessage(BaseModel):
    """Message for inter-agent communication."""
    id: UUID = field(default_factory=uuid4)
    sender_id: UUID
    recipient_id: Optional[UUID] = None  # None for broadcast
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    requires_response: bool = False
    correlation_id: Optional[UUID] = None


class ConflictResolutionRequest(BaseModel):
    """Request for conflict resolution between agents."""
    conflict_id: UUID = field(default_factory=uuid4)
    conflicting_agents: List[UUID]
    conflict_type: str
    conflict_data: Dict[str, Any]
    resolution_strategy: ConflictResolutionStrategy
    timeout_seconds: int = 30


class ConflictResolutionResult(BaseModel):
    """Result of conflict resolution."""
    conflict_id: UUID
    resolution: str
    winning_agent_id: Optional[UUID] = None
    resolution_data: Dict[str, Any] = {}
    resolved_at: datetime = field(default_factory=datetime.now)


class AgentInterface(ABC):
    """Abstract interface for agents in the multi-agent system."""
    
    @property
    @abstractmethod
    def agent_info(self) -> AgentInfo:
        """Get agent information."""
        pass
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the agent."""
        pass
    
    @abstractmethod
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task."""
        pass
    
    @abstractmethod
    async def handle_message(self, message: AgentCommunicationMessage) -> Optional[AgentCommunicationMessage]:
        """Handle an incoming message from another agent."""
        pass
    
    @abstractmethod
    async def get_capabilities(self) -> Set[AgentCapability]:
        """Get the capabilities of this agent."""
        pass
    
    @abstractmethod
    async def get_status(self) -> AgentStatus:
        """Get the current status of the agent."""
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the agent gracefully."""
        pass


class MultiAgentEvent(BaseModel):
    """Event in the multi-agent system."""
    event_type: str
    agent_id: Optional[UUID] = None
    task_id: Optional[UUID] = None
    data: Dict[str, Any] = {}
    timestamp: datetime = field(default_factory=datetime.now)


class CoordinationContext(BaseModel):
    """Context for agent coordination."""
    active_agents: List[UUID] = []
    pending_tasks: List[UUID] = []
    task_assignments: Dict[UUID, UUID] = {}  # task_id -> agent_id
    shared_state: Dict[str, Any] = {}
    coordination_strategy: CoordinationStrategy = CoordinationStrategy.ROUND_ROBIN
    last_coordination: datetime = field(default_factory=datetime.now)
