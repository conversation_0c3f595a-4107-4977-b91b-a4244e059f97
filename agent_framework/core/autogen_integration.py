"""
AutoGen integration module for enhanced tool and workbench support.

This module provides integration with AutoGen's tool system and workbench patterns,
enabling seamless use of AutoGen's capabilities within the agent framework.
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union

try:
    from autogen_core.tools import BaseTool, FunctionTool
    from autogen_core.models import ChatCompletionClient
    from autogen_ext.tools.mcp import mcp_server_tools
    from autogen_ext.tools.code_execution import PythonCodeExecutionTool
    from autogen_ext.tools.http import HttpTool
    from autogen_ext.code_executors.docker import DockerCommandLineCodeExecutor
except ImportError as e:
    logging.warning(f"AutoGen tools not available: {e}")
    BaseTool = None
    FunctionTool = None
    mcp_server_tools = None
    PythonCodeExecutionTool = None
    HttpTool = None
    DockerCommandLineCodeExecutor = None

from .config import ModelConfig
from .model_client_factory import model_client_factory


class AutoGenToolsManager:
    """
    Manager for AutoGen tools integration.
    
    Provides a unified interface for managing AutoGen tools including
    MCP tools, code execution tools, and custom function tools.
    """
    
    def __init__(self):
        """Initialize the AutoGen tools manager."""
        self.logger = logging.getLogger(__name__)
        self._tools_registry: Dict[str, BaseTool] = {}
        self._code_executor: Optional[DockerCommandLineCodeExecutor] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the tools manager."""
        if self._initialized:
            return
        
        try:
            # Initialize code executor if available
            if DockerCommandLineCodeExecutor:
                self._code_executor = DockerCommandLineCodeExecutor()
                await self._code_executor.start()
                self.logger.info("Docker code executor initialized")
            
            # Register built-in tools
            await self._register_builtin_tools()
            
            self._initialized = True
            self.logger.info("AutoGen tools manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AutoGen tools manager: {e}")
            raise
    
    async def _register_builtin_tools(self) -> None:
        """Register built-in AutoGen tools."""
        try:
            # Register Python code execution tool
            if PythonCodeExecutionTool and self._code_executor:
                code_tool = PythonCodeExecutionTool(self._code_executor)
                self._tools_registry["python_code_execution"] = code_tool
                self.logger.info("Registered Python code execution tool")
            
            # Register HTTP tool
            if HttpTool:
                http_tool = HttpTool()
                self._tools_registry["http_request"] = http_tool
                self.logger.info("Registered HTTP tool")
            
        except Exception as e:
            self.logger.warning(f"Failed to register some built-in tools: {e}")
    
    def register_mcp_tools(self, mcp_servers: Dict[str, Any]) -> List[BaseTool]:
        """
        Register MCP server tools.
        
        Args:
            mcp_servers: Dictionary of MCP server configurations
            
        Returns:
            List of registered MCP tools
        """
        if not mcp_server_tools:
            self.logger.warning("MCP tools not available")
            return []
        
        tools = []
        for server_name, server_config in mcp_servers.items():
            try:
                # Create MCP tools for the server
                server_tools = mcp_server_tools(server_config)
                for tool in server_tools:
                    tool_name = f"mcp_{server_name}_{tool.name}"
                    self._tools_registry[tool_name] = tool
                    tools.append(tool)
                
                self.logger.info(f"Registered {len(server_tools)} MCP tools from {server_name}")
                
            except Exception as e:
                self.logger.error(f"Failed to register MCP tools from {server_name}: {e}")
        
        return tools
    
    def register_function_tool(self, name: str, func: callable, description: str = "") -> BaseTool:
        """
        Register a custom function as an AutoGen tool.
        
        Args:
            name: Tool name
            func: Function to wrap
            description: Tool description
            
        Returns:
            The registered tool
        """
        if not FunctionTool:
            raise RuntimeError("AutoGen FunctionTool not available")
        
        tool = FunctionTool(func, description=description)
        self._tools_registry[name] = tool
        self.logger.info(f"Registered function tool: {name}")
        return tool
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool by name."""
        return self._tools_registry.get(name)
    
    def get_all_tools(self) -> List[BaseTool]:
        """Get all registered tools."""
        return list(self._tools_registry.values())
    
    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """Get tools by category (e.g., 'mcp', 'builtin', 'custom')."""
        if category == "mcp":
            return [tool for name, tool in self._tools_registry.items() if name.startswith("mcp_")]
        elif category == "builtin":
            return [tool for name, tool in self._tools_registry.items() 
                   if name in ["python_code_execution", "http_request"]]
        elif category == "custom":
            return [tool for name, tool in self._tools_registry.items() 
                   if not name.startswith("mcp_") and name not in ["python_code_execution", "http_request"]]
        else:
            return []
    
    async def shutdown(self) -> None:
        """Shutdown the tools manager."""
        if self._code_executor:
            await self._code_executor.stop()
            self.logger.info("Code executor stopped")
        
        self._tools_registry.clear()
        self._initialized = False
        self.logger.info("AutoGen tools manager shut down")


class AutoGenWorkbench:
    """
    AutoGen workbench integration for agent coordination.
    
    Provides workbench patterns for multi-agent coordination following
    AutoGen's design patterns and best practices.
    """
    
    def __init__(self, tools_manager: AutoGenToolsManager):
        """Initialize the workbench."""
        self.logger = logging.getLogger(__name__)
        self.tools_manager = tools_manager
        self._agents: Dict[str, Any] = {}
        self._model_clients: Dict[str, ChatCompletionClient] = {}
    
    async def create_agent_with_model(self, 
                                    agent_name: str, 
                                    model_config: ModelConfig,
                                    tools: Optional[List[str]] = None,
                                    system_message: str = "") -> Any:
        """
        Create an AutoGen agent with specific model configuration.
        
        Args:
            agent_name: Name of the agent
            model_config: Model configuration for the agent
            tools: List of tool names to assign to the agent
            system_message: System message for the agent
            
        Returns:
            Created AutoGen agent
        """
        try:
            # Create model client
            model_client = model_client_factory.create_client(model_config)
            self._model_clients[agent_name] = model_client
            
            # Get tools for the agent
            agent_tools = []
            if tools:
                for tool_name in tools:
                    tool = self.tools_manager.get_tool(tool_name)
                    if tool:
                        agent_tools.append(tool)
                    else:
                        self.logger.warning(f"Tool not found: {tool_name}")
            
            # Create AutoGen agent (this would use AutoGen's agent classes)
            # For now, we'll create a placeholder that represents the agent
            agent_info = {
                "name": agent_name,
                "model_client": model_client,
                "tools": agent_tools,
                "system_message": system_message,
                "model_config": model_config
            }
            
            self._agents[agent_name] = agent_info
            self.logger.info(f"Created AutoGen agent: {agent_name} with {model_config.provider}")
            
            return agent_info
            
        except Exception as e:
            self.logger.error(f"Failed to create agent {agent_name}: {e}")
            raise
    
    def get_agent(self, agent_name: str) -> Optional[Any]:
        """Get an agent by name."""
        return self._agents.get(agent_name)
    
    def get_all_agents(self) -> Dict[str, Any]:
        """Get all agents."""
        return self._agents.copy()
    
    async def coordinate_agents(self, task: str, agent_names: List[str]) -> Dict[str, Any]:
        """
        Coordinate multiple agents to work on a task.
        
        This implements AutoGen's coordination patterns for multi-agent collaboration.
        
        Args:
            task: Task description
            agent_names: List of agent names to coordinate
            
        Returns:
            Coordination results
        """
        results = {}
        
        for agent_name in agent_names:
            agent = self.get_agent(agent_name)
            if not agent:
                self.logger.warning(f"Agent not found: {agent_name}")
                continue
            
            try:
                # This would use AutoGen's coordination patterns
                # For now, we'll simulate the coordination
                result = {
                    "agent": agent_name,
                    "model": agent["model_config"].model,
                    "provider": agent["model_config"].provider,
                    "task": task,
                    "status": "simulated",
                    "tools_used": len(agent["tools"])
                }
                
                results[agent_name] = result
                self.logger.info(f"Agent {agent_name} processed task with {agent['model_config'].provider}")
                
            except Exception as e:
                self.logger.error(f"Agent {agent_name} failed: {e}")
                results[agent_name] = {"error": str(e)}
        
        return results
    
    async def shutdown(self) -> None:
        """Shutdown the workbench."""
        # Close all model clients
        for agent_name, client in self._model_clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
                self.logger.info(f"Closed model client for agent: {agent_name}")
            except Exception as e:
                self.logger.warning(f"Failed to close client for {agent_name}: {e}")
        
        self._agents.clear()
        self._model_clients.clear()
        self.logger.info("AutoGen workbench shut down")


# Global instances
autogen_tools_manager = AutoGenToolsManager()
autogen_workbench = AutoGenWorkbench(autogen_tools_manager)
