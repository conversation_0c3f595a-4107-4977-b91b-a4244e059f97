"""
Enhanced code analysis engine with improved context gathering and understanding.
"""

import ast
import logging
from typing import Dict, List, Any, Set
from dataclasses import dataclass

@dataclass
class CodeContext:
    """Represents comprehensive code context information."""
    file_path: str
    imports: List[str]
    classes: List[Dict[str, Any]]
    functions: List[Dict[str, Any]]
    variables: List[Dict[str, Any]]
    dependencies: Set[str]
    complexity_metrics: Dict[str, Any]
    patterns: List[Dict[str, Any]]
    potential_issues: List[Dict[str, Any]]


@dataclass
class CodeRelationship:
    """Represents relationships between code elements."""
    source: str
    target: str
    relationship_type: str  # 'calls', 'inherits', 'imports', 'uses'
    confidence: float
    context: Dict[str, Any]


class CodeAnalyzer:
    """Advanced code analyzer with deep context understanding."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._ast_cache: Dict[str, ast.AST] = {}
        self._context_cache: Dict[str, CodeContext] = {}
        
    async def analyze_code_comprehensive(self, 
                                       code_content: str, 
                                       file_path: str = "",
                                       include_relationships: bool = True) -> CodeContext:
        """
        Perform comprehensive code analysis with deep context understanding.
        
        Args:
            code_content: The code to analyze
            file_path: Path to the code file
            include_relationships: Whether to analyze code relationships
            
        Returns:
            Comprehensive code context
        """
        try:
            # Parse AST
            tree = ast.parse(code_content)
            self._ast_cache[file_path] = tree
            
            # Extract basic elements
            imports = self._extract_imports(tree)
            classes = self._extract_classes(tree, code_content)
            functions = self._extract_functions(tree, code_content)
            variables = self._extract_variables(tree)
            
            # Analyze dependencies
            dependencies = self._analyze_dependencies(imports, classes, functions)
            
            # Calculate complexity metrics
            complexity_metrics = self._calculate_complexity_metrics(tree, code_content)
            
            # Identify patterns
            patterns = self._identify_patterns(tree, code_content)
            
            # Detect potential issues
            potential_issues = self._detect_potential_issues(tree, code_content)
            
            context = CodeContext(
                file_path=file_path,
                imports=imports,
                classes=classes,
                functions=functions,
                variables=variables,
                dependencies=dependencies,
                complexity_metrics=complexity_metrics,
                patterns=patterns,
                potential_issues=potential_issues
            )
            
            self._context_cache[file_path] = context
            return context
            
        except Exception as e:
            self.logger.error(f"Code analysis failed: {e}")
            return CodeContext(
                file_path=file_path,
                imports=[],
                classes=[],
                functions=[],
                variables=[],
                dependencies=set(),
                complexity_metrics={},
                patterns=[],
                potential_issues=[{"type": "analysis_error", "message": str(e)}]
            )
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """Extract import statements."""
        imports: List[str] = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}" if module else alias.name)
        return imports
    
    def _extract_classes(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Extract class definitions with detailed information."""
        classes: List[Dict[str, Any]] = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                methods: List[Dict[str, Any]] = []
                properties: List[Dict[str, Any]] = []
                
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods.append({
                            "name": item.name,
                            "args": [arg.arg for arg in item.args.args],
                            "decorators": [ast.unparse(d) for d in item.decorator_list],
                            "is_property": any("property" in ast.unparse(d) for d in item.decorator_list),
                            "line_number": item.lineno
                        })
                
                class_info = {
                    "name": node.name,
                    "bases": [ast.unparse(base) for base in node.bases],
                    "decorators": [ast.unparse(d) for d in node.decorator_list],
                    "methods": methods,
                    "properties": properties,
                    "line_number": node.lineno,
                    "docstring": ast.get_docstring(node),
                    "complexity": self._calculate_class_complexity(node)
                }
                classes.append(class_info)
        
        return classes
    
    def _extract_functions(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Extract function definitions with detailed information."""
        functions: List[Dict[str, Any]] = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and not self._is_method(node, tree):
                function_info = {
                    "name": node.name,
                    "args": [arg.arg for arg in node.args.args],
                    "defaults": [ast.unparse(d) for d in node.args.defaults],
                    "decorators": [ast.unparse(d) for d in node.decorator_list],
                    "return_annotation": ast.unparse(node.returns) if node.returns else None,
                    "line_number": node.lineno,
                    "docstring": ast.get_docstring(node),
                    "complexity": self._calculate_function_complexity(node),
                    "calls": self._extract_function_calls(node)
                }
                functions.append(function_info)
        
        return functions
    
    def _extract_variables(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract variable assignments."""
        variables: List[Dict[str, Any]] = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        variables.append({
                            "name": target.id,
                            "type": self._infer_type(node.value),
                            "line_number": node.lineno,
                            "scope": self._determine_scope(node, tree)
                        })
        
        return variables
    
    def _analyze_dependencies(self, imports: List[str], classes: List[Dict], functions: List[Dict]) -> Set[str]:
        """Analyze code dependencies."""
        dependencies: Set[str] = set()
        
        # Add imports as dependencies
        for imp in imports:
            dependencies.add(imp.split('.')[0])
        
        # Analyze function calls and class usage
        for func in functions:
            for call in func.get('calls', []):
                if '.' in call:
                    dependencies.add(call.split('.')[0])
        
        return dependencies
    
    def _calculate_complexity_metrics(self, tree: ast.AST, code_content: str) -> Dict[str, Any]:
        """Calculate various complexity metrics."""
        lines = code_content.split('\n')
        
        return {
            "cyclomatic_complexity": self._calculate_cyclomatic_complexity(tree),
            "lines_of_code": len([line for line in lines if line.strip()]),
            "comment_ratio": self._calculate_comment_ratio(code_content),
            "function_count": len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
            "class_count": len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]),
            "nesting_depth": self._calculate_max_nesting_depth(tree)
        }
    
    def _identify_patterns(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Identify design patterns and code patterns."""
        patterns: List[Dict[str, Any]] = []
        
        # Singleton pattern detection
        if self._detect_singleton_pattern(tree):
            patterns.append({
                "type": "design_pattern",
                "name": "Singleton",
                "confidence": 0.8,
                "description": "Singleton pattern detected"
            })
        
        # Factory pattern detection
        if self._detect_factory_pattern(tree):
            patterns.append({
                "type": "design_pattern", 
                "name": "Factory",
                "confidence": 0.7,
                "description": "Factory pattern detected"
            })
        
        return patterns
    
    def _detect_potential_issues(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Detect potential code issues."""
        issues: List[Dict[str, Any]] = []
        
        # Check for long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if len(node.body) > 20:
                    issues.append({
                        "type": "code_smell",
                        "severity": "medium",
                        "message": f"Function '{node.name}' is too long ({len(node.body)} statements)",
                        "line": node.lineno,
                        "suggestion": "Consider breaking this function into smaller functions"
                    })
        
        # Check for deep nesting
        max_depth = self._calculate_max_nesting_depth(tree)
        if max_depth > 4:
            issues.append({
                "type": "code_smell",
                "severity": "medium", 
                "message": f"Code has deep nesting (depth: {max_depth})",
                "suggestion": "Consider refactoring to reduce nesting depth"
            })
        
        return issues
    
    # Helper methods
    def _is_method(self, node: ast.FunctionDef, tree: ast.AST) -> bool:
        """Check if a function is a method of a class."""
        for parent in ast.walk(tree):
            if isinstance(parent, ast.ClassDef) and node in getattr(parent, "body", []):
                return True
        return False
    
    def _calculate_class_complexity(self, node: ast.ClassDef) -> int:
        """Calculate complexity of a class."""
        return len(node.body)
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _extract_function_calls(self, node: ast.FunctionDef) -> List[str]:
        """Extract function calls within a function."""
        calls: List[str] = []
        
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                if isinstance(child.func, ast.Name):
                    calls.append(child.func.id)
                elif isinstance(child.func, ast.Attribute):
                    calls.append(ast.unparse(child.func))
        
        return calls
    
    def _infer_type(self, node: ast.AST) -> str:
        """Infer the type of a value node."""
        if isinstance(node, ast.Constant):
            return type(node.value).__name__
        elif isinstance(node, ast.List):
            return "list"
        elif isinstance(node, ast.Dict):
            return "dict"
        elif isinstance(node, ast.Call):
            if isinstance(node.func, ast.Name):
                return node.func.id
        return "unknown"
    
    def _determine_scope(self, node: ast.AST, tree: ast.AST) -> str:
        """Determine the scope of a variable."""
        # Simplified scope determination
        for parent in ast.walk(tree):
            if isinstance(parent, ast.FunctionDef) and self._node_in_body(node, parent):
                return f"function:{parent.name}"
            elif isinstance(parent, ast.ClassDef) and self._node_in_body(node, parent):
                return f"class:{parent.name}"
        return "module"
    
    def _node_in_body(self, node: ast.AST, parent: ast.AST) -> bool:
        """Check if node is in the body of parent."""
        body = getattr(parent, "body", None)
        if isinstance(body, list):
            for child in body:
                if node is child or self._node_in_subtree(node, child):
                    return True
        return False
    
    def _node_in_subtree(self, target: ast.AST, root: ast.AST) -> bool:
        """Check if target node is in the subtree of root."""
        for child in ast.walk(root):
            if child is target:
                return True
        return False
    
    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity for entire module."""
        complexity = 1
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity
    
    def _calculate_comment_ratio(self, code_content: str) -> float:
        """Calculate ratio of comment lines to total lines."""
        lines = code_content.split('\n')
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        total_lines = len([line for line in lines if line.strip()])
        
        return comment_lines / total_lines if total_lines > 0 else 0.0
    
    def _calculate_max_nesting_depth(self, tree: ast.AST) -> int:
        """Calculate maximum nesting depth."""
        max_depth = 0
        
        def calculate_depth(node: ast.AST, current_depth: int = 0) -> int:
            nonlocal max_depth
            max_depth = max(max_depth, current_depth)
            
            if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With, ast.FunctionDef, ast.ClassDef)):
                current_depth += 1
            
            for child in ast.iter_child_nodes(node):
                calculate_depth(child, current_depth)
            
            return max_depth
        
        return calculate_depth(tree)
    
    def _detect_singleton_pattern(self, tree: ast.AST) -> bool:
        """Detect singleton pattern."""
        # Simplified singleton detection
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Look for __new__ method with instance checking
                for method in node.body:
                    if isinstance(method, ast.FunctionDef) and method.name == "__new__":
                        return True
        return False
    
    def _detect_factory_pattern(self, tree: ast.AST) -> bool:
        """Detect factory pattern."""
        # Simplified factory detection
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if "create" in node.name.lower() or "factory" in node.name.lower():
                    # Check if it returns different types based on parameters
                    return True
        return False
