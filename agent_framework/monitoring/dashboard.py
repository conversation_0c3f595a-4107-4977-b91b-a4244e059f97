"""
Monitoring dashboard for the agent framework.
"""

import asyncio
import logging
from typing import Dict, List, Any
from datetime import datetime

from ..core.config import FrameworkConfig


class MonitoringDashboard:
    """
    Provides a dashboard interface for monitoring agent framework performance.
    """
    
    def __init__(self, config: FrameworkConfig):
        """Initialize the monitoring dashboard."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Dashboard state
        self._is_running = False
        self._dashboard_data: Dict[str, Any] = {}
        
        # Component references (to be injected)
        self._performance_monitor = None
        self._metrics_collector = None
        self._multi_agent_logger = None
        
        # Dashboard configuration
        self._refresh_interval = 5.0  # seconds
        self._data_retention_hours = 24
    
    def set_components(self, 
                      performance_monitor=None, 
                      metrics_collector=None, 
                      multi_agent_logger=None):
        """Set references to monitoring components."""
        self._performance_monitor = performance_monitor
        self._metrics_collector = metrics_collector
        self._multi_agent_logger = multi_agent_logger
    
    async def start_dashboard(self):
        """Start the monitoring dashboard."""
        if self._is_running:
            self.logger.warning("Dashboard is already running")
            return
        
        self._is_running = True
        self.logger.info("Monitoring dashboard started")
        
        # Start dashboard update loop
        asyncio.create_task(self._dashboard_update_loop())
    
    async def stop_dashboard(self):
        """Stop the monitoring dashboard."""
        self._is_running = False
        self.logger.info("Monitoring dashboard stopped")
    
    async def _dashboard_update_loop(self):
        """Main dashboard update loop."""
        try:
            while self._is_running:
                await self._update_dashboard_data()
                await asyncio.sleep(self._refresh_interval)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Error in dashboard update loop: {e}")
    
    async def _update_dashboard_data(self):
        """Update dashboard data from all monitoring components."""
        try:
            self._dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'system_status': await self._get_system_status(),
                'performance_metrics': self._get_performance_metrics(),
                'framework_metrics': self._get_framework_metrics(),
                'agent_status': self._get_agent_status(),
                'recent_logs': self._get_recent_logs(),
                'alerts': self._get_active_alerts()
            }
        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        status = {
            'overall_health': 'unknown',
            'components': {
                'performance_monitor': 'unknown',
                'metrics_collector': 'unknown',
                'multi_agent_logger': 'unknown'
            },
            'uptime': 'unknown'
        }
        
        # Check performance monitor
        if self._performance_monitor:
            try:
                current_metrics = self._performance_monitor.get_current_metrics()
                if current_metrics.cpu_usage < 80 and current_metrics.memory_usage < 80:
                    status['components']['performance_monitor'] = 'healthy'
                else:
                    status['components']['performance_monitor'] = 'degraded'
            except Exception:
                status['components']['performance_monitor'] = 'error'
        
        # Check metrics collector
        if self._metrics_collector:
            try:
                if self._metrics_collector._is_collecting:
                    status['components']['metrics_collector'] = 'healthy'
                else:
                    status['components']['metrics_collector'] = 'stopped'
            except Exception:
                status['components']['metrics_collector'] = 'error'
        
        # Check multi-agent logger
        if self._multi_agent_logger:
            status['components']['multi_agent_logger'] = 'healthy'
        
        # Determine overall health
        component_statuses = list(status['components'].values())
        if all(s == 'healthy' for s in component_statuses):
            status['overall_health'] = 'healthy'
        elif any(s == 'error' for s in component_statuses):
            status['overall_health'] = 'error'
        else:
            status['overall_health'] = 'degraded'
        
        return status
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        if not self._performance_monitor:
            return {}
        
        try:
            current = self._performance_monitor.get_current_metrics()
            summary = self._performance_monitor.get_metrics_summary(60)  # Last hour
            
            return {
                'current': {
                    'cpu_usage': current.cpu_usage,
                    'memory_usage': current.memory_usage,
                    'memory_available_gb': current.memory_available,
                    'active_tasks': current.active_tasks,
                    'queue_size': current.queue_size,
                    'agent_count': current.agent_count
                },
                'summary': summary
            }
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    def _get_framework_metrics(self) -> Dict[str, Any]:
        """Get framework-specific metrics."""
        if not self._metrics_collector:
            return {}
        
        try:
            return self._metrics_collector.get_metrics_report(1)  # Last hour
        except Exception as e:
            self.logger.error(f"Error getting framework metrics: {e}")
            return {}
    
    def _get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents."""
        # This would be populated by the agent orchestrator
        return {
            'total_agents': 0,
            'active_agents': 0,
            'idle_agents': 0,
            'failed_agents': 0,
            'agents': []
        }
    
    def _get_recent_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent log entries."""
        if not self._multi_agent_logger:
            return []
        
        try:
            # This would get recent logs from the multi-agent logger
            return []
        except Exception as e:
            self.logger.error(f"Error getting recent logs: {e}")
            return []
    
    def _get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get active alerts."""
        alerts = []
        
        # Check performance alerts
        if self._performance_monitor:
            try:
                current = self._performance_monitor.get_current_metrics()
                
                if current.cpu_usage > 90:
                    alerts.append({
                        'level': 'critical',
                        'component': 'system',
                        'message': f'Critical CPU usage: {current.cpu_usage:.1f}%',
                        'timestamp': datetime.now().isoformat()
                    })
                elif current.cpu_usage > 80:
                    alerts.append({
                        'level': 'warning',
                        'component': 'system',
                        'message': f'High CPU usage: {current.cpu_usage:.1f}%',
                        'timestamp': datetime.now().isoformat()
                    })
                
                if current.memory_usage > 90:
                    alerts.append({
                        'level': 'critical',
                        'component': 'system',
                        'message': f'Critical memory usage: {current.memory_usage:.1f}%',
                        'timestamp': datetime.now().isoformat()
                    })
                elif current.memory_usage > 80:
                    alerts.append({
                        'level': 'warning',
                        'component': 'system',
                        'message': f'High memory usage: {current.memory_usage:.1f}%',
                        'timestamp': datetime.now().isoformat()
                    })
                
                if current.queue_size > 100:
                    alerts.append({
                        'level': 'warning',
                        'component': 'framework',
                        'message': f'High queue size: {current.queue_size}',
                        'timestamp': datetime.now().isoformat()
                    })
                
            except Exception as e:
                self.logger.error(f"Error checking performance alerts: {e}")
        
        return alerts
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get current dashboard data."""
        return self._dashboard_data.copy()
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get a high-level system overview."""
        data = self.get_dashboard_data()
        
        return {
            'timestamp': data.get('timestamp'),
            'overall_health': data.get('system_status', {}).get('overall_health', 'unknown'),
            'active_alerts': len(data.get('alerts', [])),
            'critical_alerts': len([a for a in data.get('alerts', []) if a.get('level') == 'critical']),
            'performance': {
                'cpu_usage': data.get('performance_metrics', {}).get('current', {}).get('cpu_usage', 0),
                'memory_usage': data.get('performance_metrics', {}).get('current', {}).get('memory_usage', 0),
                'active_tasks': data.get('performance_metrics', {}).get('current', {}).get('active_tasks', 0),
                'queue_size': data.get('performance_metrics', {}).get('current', {}).get('queue_size', 0)
            }
        }
    
    def get_performance_history(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance history for the specified time period."""
        if not self._performance_monitor:
            return {}
        
        try:
            history = self._performance_monitor.get_metrics_history(hours * 60)  # Convert to minutes
            
            return {
                'period_hours': hours,
                'data_points': len(history),
                'cpu_usage': [{'timestamp': m.timestamp.isoformat(), 'value': m.cpu_usage} for m in history],
                'memory_usage': [{'timestamp': m.timestamp.isoformat(), 'value': m.memory_usage} for m in history],
                'active_tasks': [{'timestamp': m.timestamp.isoformat(), 'value': m.active_tasks} for m in history],
                'queue_size': [{'timestamp': m.timestamp.isoformat(), 'value': m.queue_size} for m in history]
            }
        except Exception as e:
            self.logger.error(f"Error getting performance history: {e}")
            return {}
    
    def export_dashboard_data(self, format: str = 'json') -> str:
        """Export dashboard data in the specified format."""
        data = self.get_dashboard_data()
        
        if format.lower() == 'json':
            import json
            return json.dumps(data, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def configure_dashboard(self, **settings):
        """Configure dashboard settings."""
        if 'refresh_interval' in settings:
            self._refresh_interval = max(1.0, float(settings['refresh_interval']))
        
        if 'data_retention_hours' in settings:
            self._data_retention_hours = max(1, int(settings['data_retention_hours']))
        
        self.logger.info(f"Dashboard configured: refresh_interval={self._refresh_interval}s, "
                        f"data_retention_hours={self._data_retention_hours}h")
