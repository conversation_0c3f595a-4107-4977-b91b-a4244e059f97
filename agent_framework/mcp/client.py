"""
Enhanced MCP client implementation following MCP 2025-06-18 specification.
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime, timezone  # fixed: use timezone-aware datetimes
import contextlib  # fixed: used for suppress()

from .types import (
    MCPServerInfo, MCPConnectionStatus, MCPError, MCPConnectionError,
    MCPTimeoutError, MCPToolCallRequest, MCPToolCallResponse,
    MCPResourceRequest, MCPResourceResponse, MCPPromptRequest, MCPPromptResponse,
    MCPInitializeRequest, MCPConnectionState,
    MCPServerCapabilities, MCPConnectionMetrics
)


class MCPClient:
    """
    Enhanced MCP client with comprehensive error handling and retry mechanisms.

    Implements the MCP 2025-06-18 specification with support for:
    - Tools, resources, and prompts
    - Connection management and recovery
    - Request/response handling with timeouts
    - Metrics and monitoring
    """

    def __init__(self, server_info: MCPServerInfo):
        """
        Initialize the MCP client.

        Args:
            server_info: Information about the MCP server
        """
        self.server_info = server_info
        self.logger = logging.getLogger(f"{__name__}.{server_info.name}")
        self.connection_state = MCPConnectionState(server_info=server_info)
        self._shutdown_event = asyncio.Event()
        self._heartbeat_task: Optional[asyncio.Task] = None

    async def connect(self) -> bool:
        """
        Connect to the MCP server.

        Returns:
            True if connection was successful
        """
        if self.connection_state.server_info.status == MCPConnectionStatus.CONNECTED:
            return True

        self.logger.info(f"Connecting to MCP server: {self.server_info.name}")
        self.connection_state.server_info.status = MCPConnectionStatus.CONNECTING

        try:
            # Start the server process
            await self._start_server_process()

            # Initialize the connection
            await self._initialize_connection()

            # Start heartbeat monitoring
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())

            self.connection_state.server_info.status = MCPConnectionStatus.CONNECTED
            # fixed: timezone-aware datetime
            self.connection_state.server_info.last_connected = datetime.now(
                timezone.utc)
            self.connection_state.server_info.connection_attempts += 1

            self.logger.info(
                f"Successfully connected to MCP server: {self.server_info.name}")
            return True

        except Exception as e:
            self.connection_state.server_info.status = MCPConnectionStatus.ERROR
            self.connection_state.server_info.last_error = str(e)
            self.logger.error(
                f"Failed to connect to MCP server {self.server_info.name}: {e}")
            await self._cleanup_connection()
            return False

    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        if self.connection_state.server_info.status == MCPConnectionStatus.DISCONNECTED:
            return

        self.logger.info(
            f"Disconnecting from MCP server: {self.server_info.name}")
        self._shutdown_event.set()

        # Cancel heartbeat task
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass

        await self._cleanup_connection()
        self.connection_state.server_info.status = MCPConnectionStatus.DISCONNECTED
        self.logger.info(
            f"Disconnected from MCP server: {self.server_info.name}")

    async def call_tool(self, request: MCPToolCallRequest) -> MCPToolCallResponse:
        """
        Call a tool on the MCP server.

        Args:
            request: The tool call request

        Returns:
            The tool call response
        """
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        start_time = time.time()

        try:
            # Prepare the JSON-RPC request
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "tools/call",
                "params": {
                    "name": request.tool_name,
                    "arguments": request.arguments
                }
            }

            # Send request and wait for response
            response = await self._send_request(rpc_request, request.timeout_seconds)

            execution_time = time.time() - start_time

            if "error" in response:
                return MCPToolCallResponse(
                    success=False,
                    error=response["error"].get("message", "Unknown error"),
                    execution_time=execution_time,
                    server_name=self.server_info.name,
                    tool_name=request.tool_name
                )

            return MCPToolCallResponse(
                success=True,
                result=response.get("result"),
                execution_time=execution_time,
                server_name=self.server_info.name,
                tool_name=request.tool_name
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Tool call failed: {e}")
            return MCPToolCallResponse(
                success=False,
                error=str(e),
                execution_time=execution_time,
                server_name=self.server_info.name,
                tool_name=request.tool_name
            )

    async def get_resource(self, request: MCPResourceRequest) -> MCPResourceResponse:
        """
        Get a resource from the MCP server.

        Args:
            request: The resource request

        Returns:
            The resource response
        """
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        try:
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "resources/read",
                "params": {
                    "uri": request.resource_uri,
                    **request.parameters
                }
            }

            response = await self._send_request(rpc_request)

            if "error" in response:
                return MCPResourceResponse(
                    success=False,
                    error=response["error"].get("message", "Unknown error"),
                    server_name=self.server_info.name,
                    resource_uri=request.resource_uri
                )

            result = response.get("result", {})
            return MCPResourceResponse(
                success=True,
                content=result.get("contents", [{}])[0].get("text"),
                mime_type=result.get("contents", [{}])[0].get("mimeType"),
                server_name=self.server_info.name,
                resource_uri=request.resource_uri
            )

        except Exception as e:
            self.logger.error(f"Resource request failed: {e}")
            return MCPResourceResponse(
                success=False,
                error=str(e),
                server_name=self.server_info.name,
                resource_uri=request.resource_uri
            )

    async def get_prompt(self, request: MCPPromptRequest) -> MCPPromptResponse:
        """
        Get a prompt from the MCP server.

        Args:
            request: The prompt request

        Returns:
            The prompt response
        """
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        try:
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "prompts/get",
                "params": {
                    "name": request.prompt_name,
                    "arguments": request.arguments
                }
            }

            response = await self._send_request(rpc_request)

            if "error" in response:
                return MCPPromptResponse(
                    success=False,
                    error=response["error"].get("message", "Unknown error"),
                    server_name=self.server_info.name,
                    prompt_name=request.prompt_name
                )

            result = response.get("result", {})
            return MCPPromptResponse(
                success=True,
                messages=result.get("messages", []),
                server_name=self.server_info.name,
                prompt_name=request.prompt_name
            )

        except Exception as e:
            self.logger.error(f"Prompt request failed: {e}")
            return MCPPromptResponse(
                success=False,
                error=str(e),
                server_name=self.server_info.name,
                prompt_name=request.prompt_name
            )

    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools from the server."""
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        try:
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "tools/list"
            }

            response = await self._send_request(rpc_request)

            if "error" in response:
                raise MCPError(response["error"].get(
                    "message", "Unknown error"))

            return response.get("result", {}).get("tools", [])

        except Exception as e:
            self.logger.error(f"Failed to list tools: {e}")
            raise

    async def list_resources(self) -> List[Dict[str, Any]]:
        """List available resources from the server."""
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        try:
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "resources/list"
            }

            response = await self._send_request(rpc_request)

            if "error" in response:
                raise MCPError(response["error"].get(
                    "message", "Unknown error"))

            return response.get("result", {}).get("resources", [])

        except Exception as e:
            self.logger.error(f"Failed to list resources: {e}")
            raise

    async def list_prompts(self) -> List[Dict[str, Any]]:
        """List available prompts from the server."""
        if not self._is_connected():
            raise MCPConnectionError(
                f"Not connected to server {self.server_info.name}")

        try:
            request_id = str(uuid4())
            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "prompts/list"
            }

            response = await self._send_request(rpc_request)

            if "error" in response:
                raise MCPError(response["error"].get(
                    "message", "Unknown error"))

            return response.get("result", {}).get("prompts", [])

        except Exception as e:
            self.logger.error(f"Failed to list prompts: {e}")
            raise

    def get_connection_metrics(self) -> MCPConnectionMetrics:
        """Get connection metrics."""
        return self.connection_state.metrics

    def _is_connected(self) -> bool:
        """Check if the client is connected."""
        return (self.connection_state.server_info.status == MCPConnectionStatus.CONNECTED and
                self.connection_state.initialized)

    async def _start_server_process(self) -> None:
        """Start the MCP server process."""
        try:
            self.connection_state.process = await asyncio.create_subprocess_exec(
                self.server_info.command,
                *self.server_info.args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={**self.server_info.env}
            )

            self.connection_state.reader = self.connection_state.process.stdout
            self.connection_state.writer = self.connection_state.process.stdin

        except Exception as e:
            raise MCPConnectionError(f"Failed to start server process: {e}")

    async def _initialize_connection(self) -> None:
        """Initialize the MCP connection."""
        try:
            # Send initialize request
            init_request = MCPInitializeRequest()
            request_id = str(uuid4())

            rpc_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "initialize",
                # Pydantic v2: use model_dump
                # type: ignore[attr-defined]
                "params": init_request.model_dump()
            }

            response = await self._send_request(rpc_request, timeout=self.server_info.timeout_seconds)

            if "error" in response:
                raise MCPConnectionError(
                    f"Initialize failed: {response['error']}")

            # Parse server capabilities
            result = response.get("result", {})
            self.connection_state.capabilities = MCPServerCapabilities(
                **result.get("capabilities", {}))
            self.connection_state.initialized = True

            # Send initialized notification
            await self._send_notification("notifications/initialized")

        except Exception as e:
            raise MCPConnectionError(f"Failed to initialize connection: {e}")

    async def _send_request(self, request: Dict[str, Any], timeout: Optional[int] = None) -> Dict[str, Any]:
        """Send a JSON-RPC request and wait for response."""
        if not self.connection_state.writer:
            raise MCPConnectionError("No connection to server")

        request_id = request.get("id")
        if not request_id:
            raise ValueError("Request must have an ID")

        # Create future for response
        response_future: asyncio.Future = asyncio.get_running_loop().create_future()
        self.connection_state.pending_requests[request_id] = response_future

        # Define timeout_value before try/except to appease strict analyzers
        timeout_value = timeout or self.server_info.timeout_seconds

        try:
            # Send request
            request_data = json.dumps(request) + "\n"
            self.connection_state.writer.write(request_data.encode())
            await self.connection_state.writer.drain()

            # Update metrics
            self.connection_state.metrics.total_requests += 1

            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout_value)

            # Update metrics
            self.connection_state.metrics.successful_requests += 1
            return response  # type: ignore[return-value]

        except asyncio.TimeoutError:
            self.connection_state.metrics.failed_requests += 1
            raise MCPTimeoutError(
                f"Request timed out after {timeout_value} seconds")
        except Exception as e:
            self.connection_state.metrics.failed_requests += 1
            raise MCPError(f"Request failed: {e}")
        finally:
            # Clean up pending request
            self.connection_state.pending_requests.pop(request_id, None)

    async def _send_notification(self, method: str, params: Optional[Dict[str, Any]] = None) -> None:
        """Send a JSON-RPC notification (no response expected)."""
        if not self.connection_state.writer:
            raise MCPConnectionError("No connection to server")

        notification: Dict[str, Any] = {
            "jsonrpc": "2.0",
            "method": method
        }

        if params is not None:
            # Assign proper typed value for "params"
            notification = {**notification, "params": params}

        notification_data = json.dumps(notification) + "\n"
        self.connection_state.writer.write(notification_data.encode())
        await self.connection_state.writer.drain()

    async def _heartbeat_loop(self) -> None:
        """Periodic heartbeat to keep connection healthy and detect failures."""
        # fixed: heartbeat_seconds might not exist; use getattr with fallback to timeout_seconds or 5
        hb = getattr(self.server_info, "heartbeat_seconds", None)
        base_interval = hb if isinstance(hb, (int, float)) else None
        if base_interval is None:
            base_interval = getattr(self.server_info, "timeout_seconds", 5)
        try:
            interval = max(1, int(base_interval))
        except Exception:
            interval = 5

        try:
            while not self._shutdown_event.is_set() and self._is_connected():
                try:
                    # Send a lightweight notification or ping method if supported
                    await self._send_notification("notifications/heartbeat", {
                        # fixed: timezone-aware timestamp
                        "timestamp": datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
                    })
                except Exception as e:
                    self.logger.warning(f"Heartbeat failed: {e}")
                    break
                await asyncio.sleep(interval)
        except asyncio.CancelledError:
            # Normal shutdown
            pass

    async def _cleanup_connection(self) -> None:
        """Clean up subprocess and IO resources safely."""
        # Close writer
        try:
            if self.connection_state.writer:
                self.connection_state.writer.close()
                with contextlib.suppress(Exception):
                    # type: ignore[attr-defined]
                    await self.connection_state.writer.wait_closed()
        except Exception:
            pass
        finally:
            self.connection_state.writer = None

        # Terminate process
        try:
            if self.connection_state.process and self.connection_state.process.returncode is None:
                self.connection_state.process.terminate()
                with contextlib.suppress(Exception):
                    await asyncio.wait_for(self.connection_state.process.wait(), timeout=5)
        except Exception:
            # Force kill if needed
            try:
                if self.connection_state.process and self.connection_state.process.returncode is None:
                    self.connection_state.process.kill()
                    with contextlib.suppress(Exception):
                        await asyncio.wait_for(self.connection_state.process.wait(), timeout=5)
            except Exception:
                pass
        finally:
            self.connection_state.process = None
            self.connection_state.reader = None

        # Reset state flags
        self.connection_state.initialized = False
