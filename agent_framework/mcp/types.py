"""
Types and data structures for MCP integration.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel


class MCPConnectionStatus(Enum):
    """Status of an MCP server connection."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    RECONNECTING = "reconnecting"


class MCPCapabilityType(Enum):
    """Types of MCP capabilities."""
    TOOLS = "tools"
    RESOURCES = "resources"
    PROMPTS = "prompts"
    SAMPLING = "sampling"
    ROOTS = "roots"
    ELICITATION = "elicitation"


class MCPTransportType(Enum):
    """Types of MCP transports."""
    STDIO = "stdio"
    SSE = "sse"
    WEBSOCKET = "websocket"


@dataclass
class MCPServerInfo:
    """Information about an MCP server."""
    name: str
    command: str
    args: List[str] = field(default_factory=list)
    env: Dict[str, str] = field(default_factory=dict)
    transport: MCPTransportType = MCPTransportType.STDIO
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    description: Optional[str] = None
    enabled: bool = True
    
    # Connection state
    status: MCPConnectionStatus = MCPConnectionStatus.DISCONNECTED
    last_connected: Optional[datetime] = None
    last_error: Optional[str] = None
    connection_attempts: int = 0
    
    # Capabilities
    supported_capabilities: List[MCPCapabilityType] = field(default_factory=list)
    tools: List[Dict[str, Any]] = field(default_factory=list)
    resources: List[Dict[str, Any]] = field(default_factory=list)
    prompts: List[Dict[str, Any]] = field(default_factory=list)


class MCPError(Exception):
    """Base exception for MCP-related errors."""
    
    def __init__(self, message: str, server_name: Optional[str] = None, error_code: Optional[str] = None):
        super().__init__(message)
        self.server_name = server_name
        self.error_code = error_code
        self.timestamp = datetime.now()


class MCPConnectionError(MCPError):
    """Exception raised when MCP connection fails."""
    pass


class MCPTimeoutError(MCPError):
    """Exception raised when MCP operation times out."""
    pass


class MCPServerNotFoundError(MCPError):
    """Exception raised when MCP server is not found."""
    pass


class MCPToolCallRequest(BaseModel):
    """Request to call an MCP tool."""
    server_name: str
    tool_name: str
    arguments: Dict[str, Any] = {}
    timeout_seconds: Optional[int] = None


class MCPToolCallResponse(BaseModel):
    """Response from an MCP tool call."""
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    server_name: str
    tool_name: str


class MCPResourceRequest(BaseModel):
    """Request to access an MCP resource."""
    server_name: str
    resource_uri: str
    parameters: Dict[str, Any] = {}


class MCPResourceResponse(BaseModel):
    """Response from an MCP resource request."""
    success: bool
    content: Optional[str] = None
    mime_type: Optional[str] = None
    error: Optional[str] = None
    server_name: str
    resource_uri: str


class MCPPromptRequest(BaseModel):
    """Request to get an MCP prompt."""
    server_name: str
    prompt_name: str
    arguments: Dict[str, Any] = {}


class MCPPromptResponse(BaseModel):
    """Response from an MCP prompt request."""
    success: bool
    messages: List[Dict[str, Any]] = []
    error: Optional[str] = None
    server_name: str
    prompt_name: str


class MCPServerCapabilities(BaseModel):
    """Capabilities reported by an MCP server."""
    tools: Optional[Dict[str, Any]] = None
    resources: Optional[Dict[str, Any]] = None
    prompts: Optional[Dict[str, Any]] = None
    sampling: Optional[Dict[str, Any]] = None
    roots: Optional[Dict[str, Any]] = None
    elicitation: Optional[Dict[str, Any]] = None


class MCPClientCapabilities(BaseModel):
    """Capabilities provided by the MCP client."""
    sampling: Optional[Dict[str, Any]] = None
    roots: Optional[Dict[str, Any]] = None
    elicitation: Optional[Dict[str, Any]] = None


class MCPInitializeRequest(BaseModel):
    """MCP initialize request."""
    protocol_version: str = "2025-06-18"
    capabilities: MCPClientCapabilities = field(default_factory=MCPClientCapabilities)
    client_info: Dict[str, Any] = field(default_factory=lambda: {
        "name": "agent-framework",
        "version": "0.1.0"
    })


class MCPInitializeResponse(BaseModel):
    """MCP initialize response."""
    protocol_version: str
    capabilities: MCPServerCapabilities
    server_info: Dict[str, Any]
    instructions: Optional[str] = None


class MCPConnectionMetrics(BaseModel):
    """Metrics for an MCP connection."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    connection_uptime: float = 0.0
    reconnection_count: int = 0


@dataclass
class MCPConnectionState:
    """State of an MCP connection."""
    server_info: MCPServerInfo
    process: Optional[asyncio.subprocess.Process] = None
    reader: Optional[asyncio.StreamReader] = None
    writer: Optional[asyncio.StreamWriter] = None
    initialized: bool = False
    capabilities: Optional[MCPServerCapabilities] = None
    metrics: MCPConnectionMetrics = field(default_factory=MCPConnectionMetrics)
    last_heartbeat: Optional[datetime] = None
    
    # Request tracking
    pending_requests: Dict[str, asyncio.Future] = field(default_factory=dict)
    request_id_counter: int = 0
