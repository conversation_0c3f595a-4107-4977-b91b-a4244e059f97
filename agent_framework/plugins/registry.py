"""
Plugin registry for managing plugin metadata and discovery.
"""

import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime

from ..core.types import PluginInterface, PluginCapability


@dataclass
class PluginMetadata:
    """Metadata for a registered plugin."""
    name: str
    version: str
    description: str
    author: str = ""
    license: str = ""
    dependencies: List[str] = field(default_factory=list)
    capabilities: List[PluginCapability] = field(default_factory=list)
    file_path: str = ""
    class_name: str = ""
    registered_at: datetime = field(default_factory=datetime.now)
    last_loaded: Optional[datetime] = None
    load_count: int = 0
    error_count: int = 0
    is_enabled: bool = True


class PluginRegistry:
    """
    Registry for managing plugin metadata and discovery.

    Maintains information about available plugins, their capabilities,
    dependencies, and loading status.
    """

    def __init__(self):
        """Initialize the plugin registry."""
        self.logger = logging.getLogger(__name__)
        self._plugins: Dict[str, PluginMetadata] = {}
        self._capabilities: Dict[str, Set[str]] = {}  # capability -> plugin names
        self._dependencies: Dict[str, Set[str]] = {}  # plugin -> dependencies

    def register_plugin(self, metadata: PluginMetadata) -> None:
        """Register a plugin with its metadata."""
        self.logger.info(f"Registering plugin: {metadata.name} v{metadata.version}")

        # Validate plugin metadata
        if not metadata.name:
            raise ValueError("Plugin name is required")

        if not metadata.version:
            raise ValueError("Plugin version is required")

        # Check for conflicts
        if metadata.name in self._plugins:
            existing = self._plugins[metadata.name]
            self.logger.warning(
                f"Plugin {metadata.name} already registered "
                f"(existing: v{existing.version}, new: v{metadata.version})"
            )

        # Register the plugin
        self._plugins[metadata.name] = metadata

        # Index capabilities
        for capability in metadata.capabilities:
            if capability.name not in self._capabilities:
                self._capabilities[capability.name] = set()
            self._capabilities[capability.name].add(metadata.name)

        # Index dependencies
        if metadata.dependencies:
            self._dependencies[metadata.name] = set(metadata.dependencies)

        self.logger.info(f"Plugin {metadata.name} registered successfully")

    def unregister_plugin(self, plugin_name: str) -> None:
        """Unregister a plugin."""
        if plugin_name not in self._plugins:
            self.logger.warning(f"Plugin {plugin_name} not found in registry")
            return

        self.logger.info(f"Unregistering plugin: {plugin_name}")

        metadata = self._plugins[plugin_name]

        # Remove from capability index
        for capability in metadata.capabilities:
            if capability.name in self._capabilities:
                self._capabilities[capability.name].discard(plugin_name)
                if not self._capabilities[capability.name]:
                    del self._capabilities[capability.name]

        # Remove from dependency index
        if plugin_name in self._dependencies:
            del self._dependencies[plugin_name]

        # Remove the plugin
        del self._plugins[plugin_name]

        self.logger.info(f"Plugin {plugin_name} unregistered")

    def get_plugin(self, plugin_name: str) -> Optional[PluginMetadata]:
        """Get plugin metadata by name."""
        return self._plugins.get(plugin_name)

    def get_all_plugins(self) -> Dict[str, PluginMetadata]:
        """Get all registered plugins."""
        return self._plugins.copy()

    def get_enabled_plugins(self) -> Dict[str, PluginMetadata]:
        """Get all enabled plugins."""
        return {
            name: metadata
            for name, metadata in self._plugins.items()
            if metadata.is_enabled
        }

    def get_plugins_by_capability(self, capability_name: str) -> List[PluginMetadata]:
        """Get all plugins that provide a specific capability."""
        plugin_names = self._capabilities.get(capability_name, set())
        return [
            self._plugins[name]
            for name in plugin_names
            if name in self._plugins and self._plugins[name].is_enabled
        ]

    def get_plugin_dependencies(self, plugin_name: str) -> Set[str]:
        """Get dependencies for a plugin."""
        return self._dependencies.get(plugin_name, set())

    def get_dependency_order(self, plugin_names: List[str]) -> List[str]:
        """Get plugins in dependency order (dependencies first)."""
        visited = set()
        result = []

        def visit(name: str):
            if name in visited:
                return

            visited.add(name)

            # Visit dependencies first
            for dep in self.get_plugin_dependencies(name):
                if dep in plugin_names:
                    visit(dep)

            result.append(name)

        for name in plugin_names:
            visit(name)

        return result

    def validate_dependencies(self, plugin_name: str) -> List[str]:
        """Validate that all dependencies for a plugin are available."""
        errors = []
        dependencies = self.get_plugin_dependencies(plugin_name)

        for dep in dependencies:
            if dep not in self._plugins:
                errors.append(f"Missing dependency: {dep}")
            elif not self._plugins[dep].is_enabled:
                errors.append(f"Dependency disabled: {dep}")

        return errors

    def enable_plugin(self, plugin_name: str) -> None:
        """Enable a plugin."""
        if plugin_name in self._plugins:
            self._plugins[plugin_name].is_enabled = True
            self.logger.info(f"Plugin {plugin_name} enabled")
        else:
            self.logger.warning(f"Plugin {plugin_name} not found")

    def disable_plugin(self, plugin_name: str) -> None:
        """Disable a plugin."""
        if plugin_name in self._plugins:
            self._plugins[plugin_name].is_enabled = False
            self.logger.info(f"Plugin {plugin_name} disabled")
        else:
            self.logger.warning(f"Plugin {plugin_name} not found")

    def update_load_stats(self, plugin_name: str, success: bool) -> None:
        """Update loading statistics for a plugin."""
        if plugin_name in self._plugins:
            metadata = self._plugins[plugin_name]
            if success:
                metadata.last_loaded = datetime.now()
                metadata.load_count += 1
            else:
                metadata.error_count += 1

    def get_registry_stats(self) -> Dict[str, int]:
        """Get registry statistics."""
        total = len(self._plugins)
        enabled = len(self.get_enabled_plugins())
        capabilities = len(self._capabilities)

        return {
            "total_plugins": total,
            "enabled_plugins": enabled,
            "disabled_plugins": total - enabled,
            "total_capabilities": capabilities,
        }

    def clear(self) -> None:
        """Clear all registered plugins."""
        self.logger.info("Clearing plugin registry")
        self._plugins.clear()
        self._capabilities.clear()
        self._dependencies.clear()