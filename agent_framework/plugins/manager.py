"""
Plugin manager for coordinating plugin lifecycle and operations.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Awaitable, Sequence, cast

from ..core.config import FrameworkConfig
from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from .registry import PluginRegistry, PluginMetadata
from .loader import PluginLoader


class PluginManager:
    """
    Central manager for plugin lifecycle and operations.

    Coordinates plugin discovery, loading, execution, and management
    across the entire framework.
    """

    def __init__(self, config: FrameworkConfig, message_broker=None):
        """Initialize the plugin manager."""
        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # Core components
        self.registry = PluginRegistry()
        self.loader = PluginLoader(
            self.registry,
            self.config.plugins.allowed_imports
        )

        # State
        self._is_initialized = False

    async def initialize(self) -> None:
        """Initialize the plugin manager."""
        if self._is_initialized:
            return

        self.logger.info("Initializing plugin manager...")

        try:
            # Discover plugins in configured directories
            await self.discover_plugins()

            self._is_initialized = True
            self.logger.info("Plugin manager initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize plugin manager: {e}")
            raise

    async def discover_plugins(self) -> None:
        """Discover plugins in configured directories."""
        self.logger.info("Discovering plugins...")

        discovered = await self.loader.discover_plugins(
            self.config.plugins.plugin_directories
        )

        # Register discovered plugins
        for metadata in discovered:
            try:
                self.registry.register_plugin(metadata)
            except Exception as e:
                self.logger.error(f"Failed to register plugin {metadata.name}: {e}")

        stats = self.registry.get_registry_stats()
        self.logger.info(f"Discovery complete: {stats['total_plugins']} plugins found")

    async def load_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Load a specific plugin."""
        return await self.loader.load_plugin(plugin_name, config)

    async def load_all_plugins(self) -> None:
        """Load all enabled plugins."""
        enabled_plugins = self.registry.get_enabled_plugins()

        if not enabled_plugins:
            self.logger.info("No enabled plugins to load")
            return

        # Get plugins in dependency order
        plugin_names = list(enabled_plugins.keys())
        ordered_names = self.registry.get_dependency_order(plugin_names)

        self.logger.info(f"Loading {len(ordered_names)} plugins in dependency order")

        for plugin_name in ordered_names:
            try:
                await self.load_plugin(plugin_name)
            except Exception as e:
                self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
                # Continue loading other plugins

    async def unload_plugin(self, plugin_name: str) -> None:
        """Unload a specific plugin."""
        await self.loader.unload_plugin(plugin_name)

    async def reload_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Reload a plugin."""
        return await self.loader.reload_plugin(plugin_name, config)

    async def execute_plugin_request(self, plugin_name: str, request: PluginRequest) -> PluginResponse:
        """Execute a request on a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            # Try to load the plugin if not loaded
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        try:
            # Apply timeout if specified
            if request.timeout_seconds:
                response = await asyncio.wait_for(
                    plugin_instance.execute(request),
                    timeout=request.timeout_seconds
                )
            else:
                response = await plugin_instance.execute(request)

            return response

        except asyncio.TimeoutError:
            return PluginResponse(
                success=False,
                error=f"Plugin execution timed out after {request.timeout_seconds}s"
            )
        except Exception as e:
            return PluginResponse(
                success=False,
                error=str(e)
            )

    async def get_plugin_capabilities(self, plugin_name: str) -> List[PluginCapability]:
        """Get capabilities for a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        return await plugin_instance.get_capabilities()

    async def get_all_capabilities(self) -> Dict[str, List[PluginCapability]]:
        """Get capabilities for all loaded plugins."""
        capabilities: Dict[str, List[PluginCapability]] = {}

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities[plugin_name] = await plugin_instance.get_capabilities()
            except Exception as e:
                self.logger.error(f"Failed to get capabilities for {plugin_name}: {e}")
                capabilities[plugin_name] = []

        return capabilities

    async def find_plugins_by_capability(self, capability_name: str) -> List[str]:
        """Find plugins that provide a specific capability."""
        matching_plugins: List[str] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities = await plugin_instance.get_capabilities()
                for capability in capabilities:
                    if capability.name == capability_name:
                        matching_plugins.append(plugin_name)
                        break
            except Exception as e:
                self.logger.error(f"Error checking capabilities for {plugin_name}: {e}")

        return matching_plugins

    async def get_all_tools(self) -> List[Any]:
        """Get all tools from loaded plugins for agent integration."""
        tools: List[Any] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                # Check if plugin has tools method
                if hasattr(plugin_instance, "get_tools"):
                    # Help type checker: getattr returns a callable returning Awaitable[Sequence[Any]] or List[Any]
                    get_tools_fn = cast(Any, getattr(plugin_instance, "get_tools"))
                    plugin_tools = await get_tools_fn()
                    if plugin_tools:
                        tools.extend(list(plugin_tools))
                        self.logger.debug(f"Added {len(plugin_tools)} tools from {plugin_name}")
            except Exception as e:
                self.logger.error(f"Error getting tools from {plugin_name}: {e}")

        self.logger.info(f"Collected {len(tools)} tools from plugins")
        return tools

    def get_loaded_plugins(self) -> Dict[str, PluginInterface]:
        """Get all currently loaded plugins."""
        return self.loader.get_loaded_plugins()

    def get_plugin_registry(self) -> PluginRegistry:
        """Get the plugin registry."""
        return self.registry

    def get_plugin_metadata(self, plugin_name: str) -> Optional[PluginMetadata]:
        """Get metadata for a specific plugin."""
        return self.registry.get_plugin(plugin_name)

    def enable_plugin(self, plugin_name: str) -> None:
        """Enable a plugin."""
        self.registry.enable_plugin(plugin_name)

    def disable_plugin(self, plugin_name: str) -> None:
        """Disable a plugin."""
        self.registry.disable_plugin(plugin_name)

    async def get_plugin_status(self) -> Dict[str, Any]:
        """Get comprehensive plugin status information."""
        registry_stats = self.registry.get_registry_stats()
        loaded_plugins = self.loader.get_loaded_plugins()

        plugin_details: Dict[str, Any] = {}
        for name, metadata in self.registry.get_all_plugins().items():
            plugin_details[name] = {
                "metadata": metadata,
                "loaded": name in loaded_plugins,
                "enabled": metadata.is_enabled,
                "load_count": metadata.load_count,
                "error_count": metadata.error_count,
                "last_loaded": metadata.last_loaded
            }

        return {
            "registry_stats": registry_stats,
            "loaded_count": len(loaded_plugins),
            "plugin_details": plugin_details
        }

    async def shutdown(self) -> None:
        """Shutdown the plugin manager and cleanup resources."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down plugin manager...")

        try:
            # Cleanup all loaded plugins
            await self.loader.cleanup_all()

            # Clear registry
            self.registry.clear()

            self._is_initialized = False
            self.logger.info("Plugin manager shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during plugin manager shutdown: {e}")

    @property
    def is_initialized(self) -> bool:
        """Check if the plugin manager is initialized."""
        return self._is_initialized