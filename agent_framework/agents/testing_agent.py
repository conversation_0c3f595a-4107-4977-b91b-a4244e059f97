"""
Specialized agent for testing tasks.
"""

import logging
from typing import Any, Dict, List, Optional

from ..core.multi_agent_types import Agent<PERSON>apability
from ..core.types import Task, TaskStatus  # Removed unused TaskResult
from ..core.config import AgentRoleConfig
from .base_agent import BaseAgent


class TestingAgent(BaseAgent):
    """
    Specialized agent for testing tasks.
    
    Capabilities:
    - Unit test generation
    - Integration test creation
    - Test case analysis
    - Test coverage assessment
    - Test execution and reporting
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs):
        """Initialize the testing agent."""
        if not role_config:
            role_config = self._create_default_config()
        
        super().__init__(role_config, **kwargs)
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for testing agent."""
        return AgentRoleConfig(
            name="testing_agent",
            description="Specialized agent for test generation and execution",
            system_message="""You are a specialized testing agent. Your role is to:

1. Generate comprehensive unit tests for given code
2. Create integration tests for system components
3. Analyze existing test suites for coverage and quality
4. Execute tests and provide detailed reports
5. Suggest test improvements and additional test cases
6. Implement test automation strategies

You have access to testing frameworks and tools. Always follow testing best practices:
- Write clear, maintainable test cases
- Ensure good test coverage
- Use appropriate assertions and mocking
- Follow the AAA pattern (Arrange, Act, Assert)
- Provide meaningful test names and descriptions""",
            capabilities=[
                AgentCapability.TESTING.value,
                AgentCapability.CODE_ANALYSIS.value,
                AgentCapability.FILE_OPERATIONS.value
            ],
            mcp_servers=["filesystem", "testing_tools"],
            max_concurrent_tasks=2,
            priority=2
        )
    
    async def generate_unit_tests(self, 
                                 code_content: str, 
                                 language: str = "python",
                                 test_framework: str = "pytest") -> str:
        """
        Generate unit tests for the given code.
        """
        task = Task(
            name="generate_unit_tests",
            description=f"Generate unit tests for {language} code using {test_framework}",
            task_type="test_generation",
            parameters={
                "code_content": code_content,
                "language": language,
                "test_framework": test_framework,
                "test_types": ["positive", "negative", "edge_cases", "boundary"]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def generate_integration_tests(self, 
                                       components: List[str],
                                       language: str = "python",
                                       test_framework: str = "pytest") -> str:
        """
        Generate integration tests for system components.
        """
        task = Task(
            name="generate_integration_tests",
            description=f"Generate integration tests for components: {', '.join(components)}",
            task_type="integration_test_generation",
            parameters={
                "components": components,
                "language": language,
                "test_framework": test_framework,
                "test_scenarios": ["happy_path", "error_handling", "data_flow"]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def analyze_test_coverage(self, 
                                   test_files: List[str],
                                   source_files: List[str]) -> Dict[str, Any]:
        """
        Analyze test coverage for the given files.
        """
        task = Task(
            name="analyze_test_coverage",
            description="Analyze test coverage for source files",
            task_type="coverage_analysis",
            parameters={
                "test_files": test_files,
                "source_files": source_files,
                "coverage_types": ["line", "branch", "function", "statement"]
            }
        )
        
        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}
    
    async def execute_tests(self, 
                           test_files: List[str],
                           test_framework: str = "pytest") -> Dict[str, Any]:
        """
        Execute tests and provide results.
        """
        task = Task(
            name="execute_tests",
            description=f"Execute tests using {test_framework}",
            task_type="test_execution",
            parameters={
                "test_files": test_files,
                "test_framework": test_framework,
                "options": ["verbose", "coverage", "junit_xml"]
            }
        )
        
        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}
    
    async def analyze_test_quality(self, test_content: str) -> Dict[str, Any]:
        """
        Analyze the quality of existing tests.
        """
        task = Task(
            name="analyze_test_quality",
            description="Analyze test code quality and effectiveness",
            task_type="test_quality_analysis",
            parameters={
                "test_content": test_content,
                "quality_metrics": [
                    "test_clarity",
                    "assertion_quality", 
                    "test_isolation",
                    "mocking_usage",
                    "test_organization"
                ]
            }
        )
        
        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}
    
    async def suggest_test_improvements(self,
                                      test_content: str,
                                      coverage_data: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Suggest improvements for existing tests.
        """
        task = Task(
            name="suggest_test_improvements",
            description="Suggest improvements for test code",
            task_type="test_improvement",
            parameters={
                "test_content": test_content,
                "coverage_data": coverage_data,
                "improvement_areas": [
                    "missing_test_cases",
                    "better_assertions",
                    "test_organization",
                    "performance_tests",
                    "error_handling_tests"
                ]
            }
        )

        result = await self.execute_task(task)
        if result.status == TaskStatus.COMPLETED and isinstance(result.result, list):
            # Filter to only strings to satisfy List[str]
            return [item for item in result.result if isinstance(item, str)]
        return []
    
    async def run_comprehensive_test_suite(self,
                                         test_files: List[str],
                                         code_files: List[str],
                                         test_framework: str = "pytest") -> Dict[str, Any]:
        """
        Run comprehensive test suite with coverage and quality analysis.
        """
        task = Task(
            name="run_comprehensive_test_suite",
            description="Run comprehensive test suite with analysis",
            task_type="comprehensive_testing",
            parameters={
                "test_files": test_files,
                "code_files": code_files,
                "test_framework": test_framework,
                "analysis_options": [
                    "coverage_analysis",
                    "performance_profiling",
                    "test_quality_metrics",
                    "failure_analysis"
                ]
            }
        )

        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}

    async def generate_function_test_data(self,
                               function_signature: str,
                               test_scenarios: List[str]) -> Dict[str, Any]:
        """
        Generate test data for various scenarios.
        Renamed to avoid shadowing with schema-based generate_test_data.
        """
        task = Task(
            name="generate_test_data",
            description="Generate test data for function testing",
            task_type="test_data_generation",
            parameters={
                "function_signature": function_signature,
                "test_scenarios": test_scenarios,
                "data_types": [
                    "valid_inputs",
                    "edge_cases",
                    "invalid_inputs",
                    "boundary_values"
                ]
            }
        )

        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}
    
    async def generate_test_data(self, 
                               schema: Dict[str, Any],
                               count: int = 10) -> List[Dict[str, Any]]:
        """
        Generate test data based on a schema.
        """
        task = Task(
            name="generate_test_data",
            description=f"Generate {count} test data items based on schema",
            task_type="test_data_generation",
            parameters={
                "schema": schema,
                "count": count,
                "data_types": ["valid", "invalid", "edge_cases", "boundary"]
            }
        )
        
        result = await self.execute_task(task)
        if result.status == TaskStatus.COMPLETED and isinstance(result.result, list):
            # Ensure returned items are dicts
            return [item for item in result.result if isinstance(item, dict)]
        return []
    
    async def create_mock_objects(self, 
                                 interfaces: List[Dict[str, Any]],
                                 language: str = "python") -> str:
        """
        Create mock objects for testing.
        """
        task = Task(
            name="create_mock_objects",
            description=f"Create mock objects for {language} interfaces",
            task_type="mock_generation",
            parameters={
                "interfaces": interfaces,
                "language": language,
                "mock_types": ["simple", "configurable", "spy", "stub"]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def generate_performance_tests(self, 
                                       code_content: str,
                                       performance_requirements: Dict[str, Any]) -> str:
        """
        Generate performance tests for code.
        """
        task = Task(
            name="generate_performance_tests",
            description="Generate performance tests with specified requirements",
            task_type="performance_test_generation",
            parameters={
                "code_content": code_content,
                "performance_requirements": performance_requirements,
                "test_types": ["load", "stress", "spike", "endurance"]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    def _prepare_task_prompt(self, task: Task) -> str:
        """Prepare a specialized prompt for testing tasks."""
        base_prompt = super()._prepare_task_prompt(task)
        
        if task.task_type == "test_generation":
            base_prompt += "\nGenerate comprehensive tests including:\n"
            base_prompt += "- Positive test cases (happy path)\n"
            base_prompt += "- Negative test cases (error conditions)\n"
            base_prompt += "- Edge cases and boundary conditions\n"
            base_prompt += "- Clear test names and documentation\n"
            base_prompt += "- Proper setup and teardown\n"
        elif task.task_type == "coverage_analysis":
            base_prompt += "\nProvide detailed coverage analysis including:\n"
            base_prompt += "- Line, branch, and function coverage percentages\n"
            base_prompt += "- Uncovered code sections\n"
            base_prompt += "- Recommendations for improving coverage\n"
            base_prompt += "- Critical paths that need testing\n"
        elif task.task_type == "test_execution":
            base_prompt += "\nExecute tests and provide:\n"
            base_prompt += "- Test results summary\n"
            base_prompt += "- Failed test details with error messages\n"
            base_prompt += "- Performance metrics if available\n"
            base_prompt += "- Recommendations for fixing failures\n"
        
        return base_prompt
