"""
Specialized agent for documentation tasks.
"""

import logging
from typing import Any, Dict, List, Optional

from ..core.multi_agent_types import Agent<PERSON>apability
from ..core.types import Task, TaskStatus  # Removed unused TaskResult
from ..core.config import AgentRoleConfig
from .base_agent import BaseAgent


class DocumentationAgent(BaseAgent):
    """
    Specialized agent for documentation tasks.
    
    Capabilities:
    - API documentation generation
    - Code documentation
    - User guide creation
    - README generation
    - Documentation quality assessment
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs):
        """Initialize the documentation agent."""
        if not role_config:
            role_config = self._create_default_config()
        
        super().__init__(role_config, **kwargs)
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for documentation agent."""
        return AgentRoleConfig(
            name="documentation_agent",
            description="Specialized agent for creating and maintaining documentation",
            system_message="""You are a specialized documentation agent. Your role is to:

1. Generate comprehensive API documentation from code
2. Create clear, user-friendly documentation
3. Write technical guides and tutorials
4. Generate README files and project documentation
5. Assess and improve existing documentation quality
6. Ensure documentation follows best practices and standards

You should create documentation that is:
- Clear and concise
- Well-structured and organized
- Accessible to the target audience
- Up-to-date with the code
- Includes examples and use cases
- Follows documentation standards (Markdown, reStructuredText, etc.)""",
            capabilities=[
                AgentCapability.DOCUMENTATION.value,
                AgentCapability.CODE_ANALYSIS.value,
                AgentCapability.FILE_OPERATIONS.value
            ],
            mcp_servers=["filesystem", "documentation_tools"],
            max_concurrent_tasks=2,
            priority=1
        )
    
    async def generate_api_documentation(self, 
                                       code_content: str,
                                       language: str = "python",
                                       format: str = "markdown") -> str:
        """
        Generate API documentation from code.
        """
        task = Task(
            name="generate_api_documentation",
            description=f"Generate API documentation for {language} code in {format} format",
            task_type="api_documentation",
            parameters={
                "code_content": code_content,
                "language": language,
                "format": format,
                "include_examples": True,
                "include_parameters": True,
                "include_return_types": True
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def generate_readme(self, 
                            project_info: Dict[str, Any],
                            code_analysis: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a README file for a project.
        """
        task = Task(
            name="generate_readme",
            description="Generate comprehensive README file",
            task_type="readme_generation",
            parameters={
                "project_info": project_info,
                "code_analysis": code_analysis,
                "sections": [
                    "title_description",
                    "installation",
                    "usage",
                    "api_reference",
                    "examples",
                    "contributing",
                    "license"
                ]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def create_user_guide(self, 
                              features: List[Dict[str, Any]],
                              target_audience: str = "developers") -> str:
        """
        Create a user guide for the application.
        """
        task = Task(
            name="create_user_guide",
            description=f"Create user guide for {target_audience}",
            task_type="user_guide",
            parameters={
                "features": features,
                "target_audience": target_audience,
                "guide_sections": [
                    "getting_started",
                    "basic_usage",
                    "advanced_features",
                    "troubleshooting",
                    "faq"
                ]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def document_code_functions(self, 
                                    code_content: str,
                                    language: str = "python") -> str:
        """
        Add documentation to code functions.
        """
        task = Task(
            name="document_code_functions",
            description=f"Add documentation to {language} functions",
            task_type="code_documentation",
            parameters={
                "code_content": code_content,
                "language": language,
                "doc_style": "google" if language == "python" else "jsdoc",
                "include_examples": True,
                "include_type_hints": True
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def assess_documentation_quality(self, 
                                         documentation_content: str,
                                         doc_type: str = "general") -> Dict[str, Any]:
        """
        Assess the quality of existing documentation.
        """
        task = Task(
            name="assess_documentation_quality",
            description=f"Assess quality of {doc_type} documentation",
            task_type="documentation_assessment",
            parameters={
                "documentation_content": documentation_content,
                "doc_type": doc_type,
                "quality_metrics": [
                    "clarity",
                    "completeness",
                    "accuracy",
                    "organization",
                    "examples",
                    "accessibility"
                ]
            }
        )
        
        result = await self.execute_task(task)
        return dict(result.result) if result.status == TaskStatus.COMPLETED and isinstance(result.result, dict) else {}
    
    async def generate_changelog(self, 
                               version_history: List[Dict[str, Any]],
                               format: str = "markdown") -> str:
        """
        Generate a changelog from version history.
        """
        task = Task(
            name="generate_changelog",
            description=f"Generate changelog in {format} format",
            task_type="changelog_generation",
            parameters={
                "version_history": version_history,
                "format": format,
                "sections": ["added", "changed", "deprecated", "removed", "fixed", "security"]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def create_tutorial(self, 
                            topic: str,
                            steps: List[Dict[str, Any]],
                            difficulty: str = "beginner") -> str:
        """
        Create a tutorial for a specific topic.
        """
        task = Task(
            name="create_tutorial",
            description=f"Create {difficulty} tutorial for {topic}",
            task_type="tutorial_creation",
            parameters={
                "topic": topic,
                "steps": steps,
                "difficulty": difficulty,
                "include_code_examples": True,
                "include_screenshots": False,
                "include_troubleshooting": True
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    async def improve_documentation(self, 
                                  current_docs: str,
                                  improvement_areas: List[str]) -> str:
        """
        Improve existing documentation.
        """
        task = Task(
            name="improve_documentation",
            description="Improve existing documentation based on identified areas",
            task_type="documentation_improvement",
            parameters={
                "current_docs": current_docs,
                "improvement_areas": improvement_areas,
                "improvements": [
                    "clarity_enhancement",
                    "structure_improvement",
                    "example_addition",
                    "completeness_check"
                ]
            }
        )
        
        result = await self.execute_task(task)
        return str(result.result) if result.status == TaskStatus.COMPLETED and result.result is not None else ""
    
    def _prepare_task_prompt(self, task: Task) -> str:
        """Prepare a specialized prompt for documentation tasks."""
        base_prompt = super()._prepare_task_prompt(task)
        
        if task.task_type == "api_documentation":
            base_prompt += "\nGenerate comprehensive API documentation including:\n"
            base_prompt += "- Clear function/method descriptions\n"
            base_prompt += "- Parameter types and descriptions\n"
            base_prompt += "- Return value information\n"
            base_prompt += "- Usage examples\n"
            base_prompt += "- Error conditions and exceptions\n"
        elif task.task_type == "readme_generation":
            base_prompt += "\nCreate a comprehensive README including:\n"
            base_prompt += "- Project description and purpose\n"
            base_prompt += "- Installation instructions\n"
            base_prompt += "- Usage examples\n"
            base_prompt += "- API reference or links\n"
            base_prompt += "- Contributing guidelines\n"
            base_prompt += "- License information\n"
        elif task.task_type == "user_guide":
            base_prompt += "\nCreate a user-friendly guide including:\n"
            base_prompt += "- Step-by-step instructions\n"
            base_prompt += "- Screenshots or diagrams where helpful\n"
            base_prompt += "- Common use cases and examples\n"
            base_prompt += "- Troubleshooting section\n"
            base_prompt += "- FAQ for common questions\n"
        
        return base_prompt
