"""
Error detection agent for identifying bugs and issues in code.
"""

import ast
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .base_agent import BaseAgent
from ..core.types import Task, TaskResult, TaskStatus
from ..core.config import FrameworkConfig


@dataclass
class ErrorDetection:
    """An error or issue detected in code."""
    type: str  # e.g., "syntax_error", "logic_error", "potential_bug"
    severity: str  # "low", "medium", "high", "critical"
    description: str
    file_path: str
    line_number: int
    column_number: Optional[int]
    code_snippet: str
    suggested_fix: Optional[str]
    confidence: float
    reasoning: str


class ErrorDetectionAgent(BaseAgent):
    """
    Agent specialized in detecting errors, bugs, and potential issues in code.
    """
    
    def __init__(self, config: FrameworkConfig):
        """Initialize the error detection agent."""
        # Create a role config for this agent
        from ..core.config import AgentRoleConfig
        role_config = AgentRoleConfig(
            name="error_detection_agent",
            description="Specialized agent for detecting errors, bugs, and potential issues in code",
            system_message="You are an error detection specialist. Analyze code to identify bugs, potential issues, and areas for improvement.",
            capabilities=["error_detection", "code_analysis"]
        )
        super().__init__(role_config)
        self.framework_config = config
        self.logger = logging.getLogger(__name__)
        
        # Error detection patterns and rules
        self._error_patterns = self._initialize_error_patterns()
        
        # Analysis state
        self._analyzed_files: Dict[str, Any] = {}
        self._detection_history: List[Dict[str, Any]] = []
    
    def _initialize_error_patterns(self) -> Dict[str, Any]:
        """Initialize error detection patterns."""
        return {
            'common_typos': [
                (r'\blenght\b', 'length', 'Common typo: lenght -> length'),
                (r'\bwidht\b', 'width', 'Common typo: widht -> width'),
                (r'\bheigth\b', 'height', 'Common typo: heigth -> height'),
            ],
            'potential_null_reference': [
                r'\.(\w+)\s*\(',  # Method calls that might be on None
            ],
            'unused_variables': [],  # Will be populated during AST analysis
            'unreachable_code': [],
            'infinite_loops': [],
            'resource_leaks': [
                r'open\s*\(',  # File operations without proper closing
            ]
        }
    
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute an error detection task."""
        try:
            self.logger.info(f"Executing error detection task: {task.name}")
            
            task_type = task.task_type.lower()
            
            if task_type == 'detect_errors':
                return await self._detect_errors(task)
            elif task_type == 'analyze_syntax':
                return await self._analyze_syntax(task)
            elif task_type == 'find_potential_bugs':
                return await self._find_potential_bugs(task)
            elif task_type == 'check_code_quality':
                return await self._check_code_quality(task)
            else:
                return TaskResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    error=f"Unknown error detection task type: {task_type}"
                )
                
        except Exception as e:
            self.logger.error(f"Error executing error detection task {task.name}: {e}")
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
    
    async def _detect_errors(self, task: Task) -> TaskResult:
        """Comprehensive error detection in code."""
        file_path = task.parameters.get('file_path')
        if not file_path:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error="file_path parameter is required"
            )
        
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            detections = []
            
            # Syntax analysis
            syntax_errors = await self._check_syntax_errors(source_code, file_path)
            detections.extend(syntax_errors)
            
            # Logic analysis (if syntax is valid)
            if not syntax_errors:
                logic_errors = await self._check_logic_errors(source_code, file_path)
                detections.extend(logic_errors)
                
                # Pattern-based detection
                pattern_errors = await self._check_pattern_errors(source_code, file_path)
                detections.extend(pattern_errors)
            
            # Store detection results
            self._analyzed_files[file_path] = {
                'detections': detections,
                'analyzed_at': task.created_at,
                'source_lines': len(source_code.splitlines())
            }
            
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.COMPLETED,
                result={
                    'file_path': file_path,
                    'detections_count': len(detections),
                    'detections': [self._detection_to_dict(d) for d in detections],
                    'summary': self._create_detection_summary(detections)
                }
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=f"Error analyzing file {file_path}: {e}"
            )
    
    async def _check_syntax_errors(self, source_code: str, file_path: str) -> List[ErrorDetection]:
        """Check for syntax errors in the code."""
        detections = []
        
        try:
            ast.parse(source_code)
        except SyntaxError as e:
            detections.append(ErrorDetection(
                type='syntax_error',
                severity='critical',
                description=f"Syntax error: {e.msg}",
                file_path=file_path,
                line_number=e.lineno or 1,
                column_number=e.offset,
                code_snippet=e.text or '',
                suggested_fix=None,
                confidence=1.0,
                reasoning="Python parser detected a syntax error"
            ))
        except Exception as e:
            detections.append(ErrorDetection(
                type='parse_error',
                severity='critical',
                description=f"Parse error: {str(e)}",
                file_path=file_path,
                line_number=1,
                column_number=None,
                code_snippet='',
                suggested_fix=None,
                confidence=1.0,
                reasoning="Failed to parse the Python code"
            ))
        
        return detections
    
    async def _check_logic_errors(self, source_code: str, file_path: str) -> List[ErrorDetection]:
        """Check for logical errors using AST analysis."""
        detections = []
        source_lines = source_code.splitlines()
        
        try:
            tree = ast.parse(source_code)
            
            # Check for unused variables
            detections.extend(self._find_unused_variables(tree, source_lines, file_path))
            
            # Check for unreachable code
            detections.extend(self._find_unreachable_code(tree, source_lines, file_path))
            
            # Check for potential infinite loops
            detections.extend(self._find_potential_infinite_loops(tree, source_lines, file_path))
            
            # Check for undefined variables
            detections.extend(self._find_undefined_variables(tree, source_lines, file_path))
            
        except Exception as e:
            self.logger.error(f"Error in logic analysis: {e}")
        
        return detections
    
    async def _check_pattern_errors(self, source_code: str, file_path: str) -> List[ErrorDetection]:
        """Check for errors using pattern matching."""
        detections = []
        source_lines = source_code.splitlines()
        
        # Check for common typos
        for line_num, line in enumerate(source_lines, 1):
            for pattern, replacement, description in self._error_patterns['common_typos']:
                if re.search(pattern, line):
                    detections.append(ErrorDetection(
                        type='typo',
                        severity='low',
                        description=description,
                        file_path=file_path,
                        line_number=line_num,
                        column_number=None,
                        code_snippet=line.strip(),
                        suggested_fix=re.sub(pattern, replacement, line),
                        confidence=0.8,
                        reasoning="Common typo pattern detected"
                    ))
        
        # Check for potential resource leaks
        for line_num, line in enumerate(source_lines, 1):
            for pattern in self._error_patterns['resource_leaks']:
                if re.search(pattern, line) and 'with ' not in line:
                    detections.append(ErrorDetection(
                        type='resource_leak',
                        severity='medium',
                        description="File opened without proper context manager",
                        file_path=file_path,
                        line_number=line_num,
                        column_number=None,
                        code_snippet=line.strip(),
                        suggested_fix="Use 'with open(...) as f:' for proper resource management",
                        confidence=0.7,
                        reasoning="File operations should use context managers to ensure proper cleanup"
                    ))
        
        return detections
    
    def _find_unused_variables(self, tree: ast.AST, source_lines: List[str], file_path: str) -> List[ErrorDetection]:
        """Find variables that are defined but never used."""
        detections = []
        
        # Simple implementation - would need more sophisticated analysis for production
        defined_vars = set()
        used_vars = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                if isinstance(node.ctx, ast.Store):
                    defined_vars.add(node.id)
                elif isinstance(node.ctx, ast.Load):
                    used_vars.add(node.id)
        
        unused_vars = defined_vars - used_vars
        
        for var_name in unused_vars:
            # Find the line where the variable is defined
            for line_num, line in enumerate(source_lines, 1):
                if f"{var_name} =" in line or f"{var_name}:" in line:
                    detections.append(ErrorDetection(
                        type='unused_variable',
                        severity='low',
                        description=f"Variable '{var_name}' is defined but never used",
                        file_path=file_path,
                        line_number=line_num,
                        column_number=None,
                        code_snippet=line.strip(),
                        suggested_fix=f"Remove unused variable '{var_name}' or use it",
                        confidence=0.6,
                        reasoning="Unused variables clutter code and may indicate logic errors"
                    ))
                    break
        
        return detections
    
    def _find_unreachable_code(self, tree: ast.AST, source_lines: List[str], file_path: str) -> List[ErrorDetection]:
        """Find code that can never be executed."""
        detections = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Check for code after return statements
                for i, stmt in enumerate(node.body):
                    if isinstance(stmt, ast.Return):
                        # Check if there are more statements after this return
                        if i < len(node.body) - 1:
                            next_stmt = node.body[i + 1]
                            detections.append(ErrorDetection(
                                type='unreachable_code',
                                severity='medium',
                                description="Code after return statement is unreachable",
                                file_path=file_path,
                                line_number=next_stmt.lineno,
                                column_number=None,
                                code_snippet=source_lines[next_stmt.lineno - 1].strip(),
                                suggested_fix="Remove unreachable code or restructure logic",
                                confidence=0.9,
                                reasoning="Code after return statements will never execute"
                            ))
        
        return detections
    
    def _find_potential_infinite_loops(self, tree: ast.AST, source_lines: List[str], file_path: str) -> List[ErrorDetection]:
        """Find loops that might run infinitely."""
        detections = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.While):
                # Check for while True without break
                if (isinstance(node.test, ast.Constant) and node.test.value is True):
                    has_break = any(isinstance(n, ast.Break) for n in ast.walk(node))
                    if not has_break:
                        detections.append(ErrorDetection(
                            type='infinite_loop',
                            severity='high',
                            description="Potential infinite loop: 'while True' without break",
                            file_path=file_path,
                            line_number=node.lineno,
                            column_number=None,
                            code_snippet=source_lines[node.lineno - 1].strip(),
                            suggested_fix="Add break condition or modify loop condition",
                            confidence=0.8,
                            reasoning="while True loops without break statements will run forever"
                        ))
        
        return detections
    
    def _find_undefined_variables(self, tree: ast.AST, source_lines: List[str], file_path: str) -> List[ErrorDetection]:
        """Find variables that are used but not defined."""
        detections = []
        
        # This is a simplified implementation
        # A full implementation would need scope analysis
        
        return detections
    
    def _detection_to_dict(self, detection: ErrorDetection) -> Dict[str, Any]:
        """Convert an error detection to a dictionary."""
        return {
            'type': detection.type,
            'severity': detection.severity,
            'description': detection.description,
            'file_path': detection.file_path,
            'line_number': detection.line_number,
            'column_number': detection.column_number,
            'code_snippet': detection.code_snippet,
            'suggested_fix': detection.suggested_fix,
            'confidence': detection.confidence,
            'reasoning': detection.reasoning
        }
    
    def _create_detection_summary(self, detections: List[ErrorDetection]) -> Dict[str, Any]:
        """Create a summary of detection results."""
        by_type = {}
        by_severity = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
        
        for detection in detections:
            by_type[detection.type] = by_type.get(detection.type, 0) + 1
            by_severity[detection.severity] += 1
        
        return {
            'total_detections': len(detections),
            'by_type': by_type,
            'by_severity': by_severity,
            'critical_issues': by_severity['critical'],
            'high_confidence_detections': len([d for d in detections if d.confidence > 0.8])
        }
    
    async def _analyze_syntax(self, task: Task) -> TaskResult:
        """Analyze syntax specifically."""
        # Implementation for syntax-only analysis
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Syntax analysis completed"
        )
    
    async def _find_potential_bugs(self, task: Task) -> TaskResult:
        """Find potential bugs in code."""
        # Implementation for bug detection
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Bug detection completed"
        )
    
    async def _check_code_quality(self, task: Task) -> TaskResult:
        """Check overall code quality."""
        # Implementation for code quality checks
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Code quality check completed"
        )
