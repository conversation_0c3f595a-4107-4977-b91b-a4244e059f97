"""
Refactoring agent for code improvement and restructuring.
"""

import ast
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .base_agent import BaseAgent
from ..core.types import Task, TaskResult, TaskStatus
from ..core.config import FrameworkConfig


@dataclass
class RefactoringSuggestion:
    """A code refactoring suggestion."""
    type: str  # e.g., "extract_method", "rename_variable", "simplify_condition"
    description: str
    file_path: str
    line_start: int
    line_end: int
    original_code: str
    suggested_code: str
    confidence: float
    impact: str  # "low", "medium", "high"
    reasoning: str


class RefactoringAgent(BaseAgent):
    """
    Agent specialized in code refactoring and improvement suggestions.
    """
    
    def __init__(self, config: FrameworkConfig):
        """Initialize the refactoring agent."""
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
        
        # Refactoring rules and patterns
        self._refactoring_rules = self._initialize_refactoring_rules()
        
        # Analysis state
        self._analyzed_files: Dict[str, Any] = {}
        self._refactoring_history: List[Dict[str, Any]] = []
    
    def _initialize_refactoring_rules(self) -> Dict[str, Any]:
        """Initialize refactoring rules and patterns."""
        return {
            'long_method': {
                'threshold': 50,  # lines
                'description': 'Methods longer than 50 lines should be broken down'
            },
            'long_parameter_list': {
                'threshold': 5,  # parameters
                'description': 'Methods with more than 5 parameters should use parameter objects'
            },
            'duplicate_code': {
                'min_lines': 5,
                'description': 'Duplicate code blocks should be extracted into methods'
            },
            'complex_condition': {
                'max_complexity': 3,
                'description': 'Complex conditions should be extracted into methods'
            },
            'magic_numbers': {
                'description': 'Magic numbers should be replaced with named constants'
            },
            'deep_nesting': {
                'max_depth': 4,
                'description': 'Deeply nested code should be refactored'
            }
        }
    
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a refactoring task."""
        try:
            self.logger.info(f"Executing refactoring task: {task.name}")
            
            task_type = task.task_type.lower()
            
            if task_type == 'analyze_refactoring':
                return await self._analyze_refactoring_opportunities(task)
            elif task_type == 'suggest_refactoring':
                return await self._suggest_refactoring(task)
            elif task_type == 'apply_refactoring':
                return await self._apply_refactoring(task)
            elif task_type == 'validate_refactoring':
                return await self._validate_refactoring(task)
            else:
                return TaskResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    error=f"Unknown refactoring task type: {task_type}"
                )
                
        except Exception as e:
            self.logger.error(f"Error executing refactoring task {task.name}: {e}")
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
    
    async def _analyze_refactoring_opportunities(self, task: Task) -> TaskResult:
        """Analyze code for refactoring opportunities."""
        file_path = task.parameters.get('file_path')
        if not file_path:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error="file_path parameter is required"
            )
        
        try:
            # Read and parse the file
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            tree = ast.parse(source_code)
            
            # Analyze for various refactoring opportunities
            suggestions = []
            
            # Check for long methods
            suggestions.extend(self._find_long_methods(tree, source_code, file_path))
            
            # Check for long parameter lists
            suggestions.extend(self._find_long_parameter_lists(tree, source_code, file_path))
            
            # Check for complex conditions
            suggestions.extend(self._find_complex_conditions(tree, source_code, file_path))
            
            # Check for magic numbers
            suggestions.extend(self._find_magic_numbers(tree, source_code, file_path))
            
            # Check for deep nesting
            suggestions.extend(self._find_deep_nesting(tree, source_code, file_path))
            
            # Store analysis results
            self._analyzed_files[file_path] = {
                'suggestions': suggestions,
                'analyzed_at': task.created_at,
                'source_lines': len(source_code.splitlines())
            }
            
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.COMPLETED,
                result={
                    'file_path': file_path,
                    'suggestions_count': len(suggestions),
                    'suggestions': [self._suggestion_to_dict(s) for s in suggestions],
                    'analysis_summary': self._create_analysis_summary(suggestions)
                }
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=f"Error analyzing file {file_path}: {e}"
            )
    
    def _find_long_methods(self, tree: ast.AST, source_code: str, file_path: str) -> List[RefactoringSuggestion]:
        """Find methods that are too long."""
        suggestions = []
        source_lines = source_code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_lines = node.end_lineno - node.lineno + 1
                
                if method_lines > self._refactoring_rules['long_method']['threshold']:
                    original_code = '\n'.join(source_lines[node.lineno-1:node.end_lineno])
                    
                    suggestions.append(RefactoringSuggestion(
                        type='extract_method',
                        description=f"Method '{node.name}' is {method_lines} lines long and should be broken down",
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.end_lineno,
                        original_code=original_code,
                        suggested_code=f"# Consider breaking down {node.name}() into smaller methods",
                        confidence=0.8,
                        impact='medium',
                        reasoning=f"Methods longer than {self._refactoring_rules['long_method']['threshold']} lines are harder to understand and maintain"
                    ))
        
        return suggestions
    
    def _find_long_parameter_lists(self, tree: ast.AST, source_code: str, file_path: str) -> List[RefactoringSuggestion]:
        """Find methods with too many parameters."""
        suggestions = []
        source_lines = source_code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                param_count = len(node.args.args)
                
                if param_count > self._refactoring_rules['long_parameter_list']['threshold']:
                    original_code = '\n'.join(source_lines[node.lineno-1:node.end_lineno])
                    
                    suggestions.append(RefactoringSuggestion(
                        type='introduce_parameter_object',
                        description=f"Method '{node.name}' has {param_count} parameters and should use a parameter object",
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.end_lineno,
                        original_code=original_code,
                        suggested_code=f"# Consider using a parameter object for {node.name}()",
                        confidence=0.7,
                        impact='medium',
                        reasoning=f"Methods with more than {self._refactoring_rules['long_parameter_list']['threshold']} parameters are hard to use and maintain"
                    ))
        
        return suggestions
    
    def _find_complex_conditions(self, tree: ast.AST, source_code: str, file_path: str) -> List[RefactoringSuggestion]:
        """Find complex conditional expressions."""
        suggestions = []
        source_lines = source_code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.If):
                complexity = self._calculate_condition_complexity(node.test)
                
                if complexity > self._refactoring_rules['complex_condition']['max_complexity']:
                    original_code = '\n'.join(source_lines[node.lineno-1:node.end_lineno])
                    
                    suggestions.append(RefactoringSuggestion(
                        type='extract_condition',
                        description=f"Complex condition with complexity {complexity} should be extracted into a method",
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.end_lineno,
                        original_code=original_code,
                        suggested_code="# Consider extracting condition into a descriptive method",
                        confidence=0.6,
                        impact='low',
                        reasoning=f"Complex conditions with complexity > {self._refactoring_rules['complex_condition']['max_complexity']} are hard to understand"
                    ))
        
        return suggestions
    
    def _find_magic_numbers(self, tree: ast.AST, source_code: str, file_path: str) -> List[RefactoringSuggestion]:
        """Find magic numbers that should be constants."""
        suggestions = []
        source_lines = source_code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Constant) and isinstance(node.value, (int, float)):
                # Skip common non-magic numbers
                if node.value in [0, 1, -1, 2, 10, 100]:
                    continue
                
                suggestions.append(RefactoringSuggestion(
                    type='replace_magic_number',
                    description=f"Magic number {node.value} should be replaced with a named constant",
                    file_path=file_path,
                    line_start=node.lineno,
                    line_end=node.end_lineno or node.lineno,
                    original_code=str(node.value),
                    suggested_code=f"CONSTANT_NAME = {node.value}  # Replace magic number",
                    confidence=0.5,
                    impact='low',
                    reasoning="Magic numbers make code less readable and maintainable"
                ))
        
        return suggestions
    
    def _find_deep_nesting(self, tree: ast.AST, source_code: str, file_path: str) -> List[RefactoringSuggestion]:
        """Find deeply nested code structures."""
        suggestions = []
        source_lines = source_code.splitlines()
        
        def calculate_nesting_depth(node, depth=0):
            max_depth = depth
            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                    child_depth = calculate_nesting_depth(child, depth + 1)
                    max_depth = max(max_depth, child_depth)
                else:
                    child_depth = calculate_nesting_depth(child, depth)
                    max_depth = max(max_depth, child_depth)
            return max_depth
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                nesting_depth = calculate_nesting_depth(node)
                
                if nesting_depth > self._refactoring_rules['deep_nesting']['max_depth']:
                    original_code = '\n'.join(source_lines[node.lineno-1:node.end_lineno])
                    
                    suggestions.append(RefactoringSuggestion(
                        type='reduce_nesting',
                        description=f"Method '{node.name}' has nesting depth {nesting_depth} and should be refactored",
                        file_path=file_path,
                        line_start=node.lineno,
                        line_end=node.end_lineno,
                        original_code=original_code,
                        suggested_code=f"# Consider reducing nesting in {node.name}() using early returns or guard clauses",
                        confidence=0.7,
                        impact='medium',
                        reasoning=f"Deep nesting (>{self._refactoring_rules['deep_nesting']['max_depth']}) makes code hard to understand"
                    ))
        
        return suggestions
    
    def _calculate_condition_complexity(self, node: ast.AST) -> int:
        """Calculate the complexity of a conditional expression."""
        if isinstance(node, ast.BoolOp):
            return 1 + sum(self._calculate_condition_complexity(value) for value in node.values)
        elif isinstance(node, ast.Compare):
            return len(node.comparators)
        elif isinstance(node, ast.UnaryOp) and isinstance(node.op, ast.Not):
            return 1 + self._calculate_condition_complexity(node.operand)
        else:
            return 1
    
    def _suggestion_to_dict(self, suggestion: RefactoringSuggestion) -> Dict[str, Any]:
        """Convert a refactoring suggestion to a dictionary."""
        return {
            'type': suggestion.type,
            'description': suggestion.description,
            'file_path': suggestion.file_path,
            'line_start': suggestion.line_start,
            'line_end': suggestion.line_end,
            'confidence': suggestion.confidence,
            'impact': suggestion.impact,
            'reasoning': suggestion.reasoning
        }
    
    def _create_analysis_summary(self, suggestions: List[RefactoringSuggestion]) -> Dict[str, Any]:
        """Create a summary of the analysis results."""
        by_type = {}
        by_impact = {'low': 0, 'medium': 0, 'high': 0}
        
        for suggestion in suggestions:
            by_type[suggestion.type] = by_type.get(suggestion.type, 0) + 1
            by_impact[suggestion.impact] += 1
        
        return {
            'total_suggestions': len(suggestions),
            'by_type': by_type,
            'by_impact': by_impact,
            'high_confidence_suggestions': len([s for s in suggestions if s.confidence > 0.7])
        }
    
    async def _suggest_refactoring(self, task: Task) -> TaskResult:
        """Suggest specific refactoring actions."""
        # Implementation for suggesting specific refactoring actions
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Refactoring suggestions generated"
        )
    
    async def _apply_refactoring(self, task: Task) -> TaskResult:
        """Apply a refactoring suggestion."""
        # Implementation for applying refactoring
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Refactoring applied"
        )
    
    async def _validate_refactoring(self, task: Task) -> TaskResult:
        """Validate that a refactoring was successful."""
        # Implementation for validating refactoring
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.COMPLETED,
            result="Refactoring validated"
        )
