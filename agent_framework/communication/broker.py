"""
Message broker for event-driven communication between components.
"""

import asyncio
import logging
from typing import Callable, Dict, List, Optional
from collections import defaultdict

from ..core.config import FrameworkConfig
from ..core.types import AgentEvent


class MessageBroker:
    """
    Event-driven message broker for component communication.

    Provides publish-subscribe messaging for loose coupling
    between framework components.
    """

    def __init__(self, config: FrameworkConfig):
        """Initialize the message broker."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Event subscribers
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._is_running = False
        self._processor_task: Optional[asyncio.Task] = None

    async def initialize(self) -> None:
        """Initialize the message broker."""
        self.logger.info("Initializing message broker...")

        # Start event processor
        self._is_running = True
        self._processor_task = asyncio.create_task(self._process_events())

        self.logger.info("Message broker initialized")

    async def publish_event(self, event: AgentEvent) -> None:
        """Publish an event to all subscribers."""
        if not self._is_running:
            return

        await self._event_queue.put(event)

    def subscribe(self, event_type: str, callback: Callable[[AgentEvent], None]) -> None:
        """Subscribe to events of a specific type."""
        self._subscribers[event_type].append(callback)
        self.logger.debug(f"Subscribed to {event_type} events")

    def unsubscribe(self, event_type: str, callback: Callable[[AgentEvent], None]) -> None:
        """Unsubscribe from events of a specific type."""
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(callback)
                self.logger.debug(f"Unsubscribed from {event_type} events")
            except ValueError:
                pass

    async def _process_events(self) -> None:
        """Process events from the queue."""
        while self._is_running:
            try:
                # Wait for events with timeout
                event = await asyncio.wait_for(
                    self._event_queue.get(),
                    timeout=1.0
                )

                # Notify subscribers
                await self._notify_subscribers(event)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing event: {e}")

    async def _notify_subscribers(self, event: AgentEvent) -> None:
        """Notify all subscribers of an event."""
        subscribers = self._subscribers.get(event.event_type, [])

        if not subscribers:
            return

        # Notify all subscribers concurrently
        tasks = []
        for callback in subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    tasks.append(callback(event))
                else:
                    # Run sync callbacks in thread pool
                    tasks.append(asyncio.get_event_loop().run_in_executor(
                        None, callback, event
                    ))
            except Exception as e:
                self.logger.error(f"Error creating callback task: {e}")

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def shutdown(self) -> None:
        """Shutdown the message broker."""
        self.logger.info("Shutting down message broker...")

        self._is_running = False

        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass

        # Clear subscribers
        self._subscribers.clear()

        self.logger.info("Message broker shutdown complete")