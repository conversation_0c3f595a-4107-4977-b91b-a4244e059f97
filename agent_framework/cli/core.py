"""
Core CLI application for the agent framework.

Provides the main CLI application structure with argument parsing,
command routing, and configuration management.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Optional, TYPE_CHECKING

try:
    import colorama  # type: ignore[import-not-found]
    from colorama import Fore, Style  # type: ignore[import-not-found]
    _COLORAMA_AVAILABLE = True
except Exception:
    # Fallbacks when colorama or its stubs are unavailable
    class _NoColor:
        def __getattr__(self, _: str) -> str:
            return ""
    colorama = None  # type: ignore[assignment]
    Fore = _NoColor()  # type: ignore[assignment]
    Style = _NoColor()  # type: ignore[assignment]
    _COLORAMA_AVAILABLE = False

if TYPE_CHECKING:
    # Only import for type checking, avoids runtime/stub issues
    from ..core.orchestrator import AgentOrchestrator
    from ..core.config import FrameworkConfig
else:
    AgentOrchestrator = object  # type: ignore[assignment,misc]
    FrameworkConfig = object  # type: ignore[assignment,misc]

from ..core.config import FrameworkConfig as RuntimeFrameworkConfig
from ..core.orchestrator import AgentOrchestrator as RuntimeAgentOrchestrator
from .commands import CommandRegistry
from .interactive import InteractiveCLI
from .utils import CLIUtils, ProgressIndicator


class AgentCLI:
    """
    Main CLI application for the agent framework.
    
    Provides command-line interface with subcommands, configuration management,
    and interactive mode support.
    """
    
    def __init__(self):
        """Initialize the CLI application."""
        if _COLORAMA_AVAILABLE:
            colorama.init(autoreset=True)  # type: ignore[union-attr]
        self.config: Optional[FrameworkConfig] = None
        self.orchestrator: Optional[AgentOrchestrator] = None
        self.command_registry = CommandRegistry()
        self.utils = CLIUtils()
        self.progress = ProgressIndicator()
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser."""
        parser = argparse.ArgumentParser(
            prog='agent-framework',
            description='Comprehensive programming assistant agent framework',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  agent-framework analyze code.py              # Analyze a Python file
  agent-framework generate function --name foo # Generate a function
  agent-framework interactive                  # Start interactive mode
  agent-framework optimize --file code.py     # Optimize code performance
  agent-framework debug --traceback error.txt # Debug error traceback
            """
        )
        
        # Global options
        parser.add_argument(
            '--config', '-c',
            type=str,
            help='Path to configuration file'
        )
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='Enable verbose output'
        )
        parser.add_argument(
            '--quiet', '-q',
            action='store_true',
            help='Suppress non-essential output'
        )
        parser.add_argument(
            '--no-color',
            action='store_true',
            help='Disable colored output'
        )
        parser.add_argument(
            '--version',
            action='version',
            version='%(prog)s 1.0.0'
        )
        
        # Create subparsers
        subparsers = parser.add_subparsers(
            dest='command',
            help='Available commands',
            metavar='COMMAND'
        )
        
        # Register all commands
        self.command_registry.register_commands(subparsers)
        
        return parser
    
    async def initialize(self, config_path: Optional[str] = None) -> None:
        """Initialize the framework with configuration."""
        try:
            # Load configuration
            if config_path and Path(config_path).exists():
                self.config = RuntimeFrameworkConfig.from_file(config_path)
                self.utils.print_success(f"Loaded configuration from {config_path}")
            else:
                self.config = RuntimeFrameworkConfig.from_env()
                if not config_path:
                    self.utils.print_info("Using default configuration with environment variables")
                else:
                    self.utils.print_warning(f"Configuration file not found: {config_path}")
                    self.utils.print_info("Using default configuration")
            
            # Initialize orchestrator
            self.orchestrator = RuntimeAgentOrchestrator(self.config)  # type: ignore[arg-type]
            
            # Show initialization progress
            with self.progress.spinner("Initializing agent framework..."):
                await self.orchestrator.initialize()  # type: ignore[union-attr]
            
            self.utils.print_success("Agent framework initialized successfully!")
            
        except Exception as e:
            self.utils.print_error(f"Failed to initialize framework: {e}")
            raise
    
    async def run(self, args: Optional[List[str]] = None) -> int:
        """Run the CLI application."""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)
        
        # Configure output based on arguments
        if parsed_args.no_color and _COLORAMA_AVAILABLE:
            colorama.deinit()  # type: ignore[union-attr]
        
        if parsed_args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        elif parsed_args.quiet:
            logging.getLogger().setLevel(logging.WARNING)
        
        try:
            # Initialize framework
            await self.initialize(parsed_args.config)
            
            # Ensure initialized objects are available for type-checker and runtime
            if self.config is None or self.orchestrator is None:
                self.utils.print_error("Framework not initialized correctly.")
                return 1
            
            # Handle commands
            if not parsed_args.command:
                # No command specified, show help
                parser.print_help()
                return 0
            
            if parsed_args.command == 'interactive':
                # Start interactive mode
                interactive_cli = InteractiveCLI(self.orchestrator, self.config)  # type: ignore[arg-type]
                return await interactive_cli.run()
            else:
                # Execute specific command
                return await self._execute_command(parsed_args)
        
        except KeyboardInterrupt:
            self.utils.print_warning("\nOperation cancelled by user")
            return 130
        except Exception as e:
            self.utils.print_error(f"Error: {e}")
            if parsed_args.verbose:
                import traceback
                traceback.print_exc()
            return 1
        finally:
            # Cleanup
            if self.orchestrator and hasattr(self.orchestrator, "shutdown"):
                await self.orchestrator.shutdown()  # type: ignore[union-attr]
    
    async def _execute_command(self, args: argparse.Namespace) -> int:
        """Execute a specific command."""
        command_name = args.command
        
        # Get command handler from registry
        command_handler = self.command_registry.get_command(command_name)
        if not command_handler:
            self.utils.print_error(f"Unknown command: {command_name}")
            return 1
        
        # Ensure config and orchestrator are initialized
        if self.orchestrator is None or self.config is None:
            self.utils.print_error("Framework is not initialized.")
            return 1
        
        try:
            # Execute command
            result = await command_handler.execute(args, self.orchestrator, self.config)  # type: ignore[arg-type]
            
            if result.get('success', True):
                if 'message' in result:
                    self.utils.print_success(result['message'])
                return 0
            else:
                if 'error' in result:
                    self.utils.print_error(result['error'])
                return 1
        
        except Exception as e:
            self.utils.print_error(f"Command execution failed: {e}")
            return 1
    
    def print_banner(self) -> None:
        """Print application banner."""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    Agent Framework CLI                       ║
║              Comprehensive Programming Assistant             ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.GREEN}Available capabilities:{Style.RESET_ALL}
  • Code analysis and understanding
  • Code generation and modification  
  • Error detection and debugging
  • Performance optimization
  • Documentation generation
  • Interactive assistance

{Fore.YELLOW}Type 'agent-framework --help' for usage information{Style.RESET_ALL}
{Fore.YELLOW}Type 'agent-framework interactive' for interactive mode{Style.RESET_ALL}
        """
        print(banner)


def main() -> int:
    """Main entry point for the CLI application."""
    try:
        cli = AgentCLI()
        return asyncio.run(cli.run())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Operation cancelled by user{Style.RESET_ALL}")
        return 130
    except Exception as e:
        print(f"{Fore.RED}Fatal error: {e}{Style.RESET_ALL}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
