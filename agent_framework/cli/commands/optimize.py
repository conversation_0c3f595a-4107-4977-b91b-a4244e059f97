"""
Optimize command for the CLI.

Provides code optimization capabilities including performance improvements,
memory optimization, and algorithmic enhancements.
"""

import argparse
from typing import Any, Dict

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from ...core.agent_orchestrator import AdvancedAgentOrchestrator, AgentCapabilities
from .base import (
    AsyncCommandBase, FileInputMixin, OutputFormatMixin,
    PluginCommandMixin, ValidationMixin
)


class OptimizeCommand(AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
                     PluginCommandMixin, ValidationMixin):
    """Command for optimizing code performance and efficiency."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "optimize"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Optimize code for performance and efficiency"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # File input arguments
        self.add_file_arguments(parser)
        
        # Optimization type arguments
        opt_group = parser.add_argument_group('Optimization Options')
        opt_group.add_argument(
            '--type', '-t',
            choices=['performance', 'memory', 'algorithms', 'bottlenecks', 'refactor', 'all'],
            default='performance',
            help='Type of optimization to perform (default: performance)'
        )
        opt_group.add_argument(
            '--target',
            choices=['speed', 'memory', 'both'],
            default='speed',
            help='Optimization target (default: speed)'
        )
        opt_group.add_argument(
            '--aggressive',
            action='store_true',
            help='Apply aggressive optimizations'
        )
        opt_group.add_argument(
            '--safe-only',
            action='store_true',
            help='Apply only safe optimizations'
        )
        opt_group.add_argument(
            '--show-diff',
            action='store_true',
            help='Show differences between original and optimized code'
        )
        
        # Plugin arguments
        self.add_plugin_arguments(parser)
        
        # Output arguments
        self.add_output_arguments(parser)
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Optimize Python code for better performance and efficiency.

Optimization Types:
  performance   - General performance optimizations
  memory        - Memory usage optimizations
  algorithms    - Algorithmic improvements
  bottlenecks   - Identify and fix performance bottlenecks
  refactor      - Refactor for efficiency
  all           - All optimization types

Examples:
  agent-framework optimize --file slow_code.py
  agent-framework optimize --code "for i in range(len(items)):" --type performance
  agent-framework optimize --file app.py --target memory --show-diff
  agent-framework optimize --stdin --type algorithms --aggressive
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the optimize command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Get code input
            code = self.get_code_input(args)
            
            # Validate code
            if not self.validate_python_code(code):
                return {
                    "success": False,
                    "error": "Invalid Python code syntax"
                }
            
            # Perform optimization
            results = await self._perform_optimization(
                code, args.type, args.target, args.aggressive, 
                args.safe_only, orchestrator
            )
            
            # Format and output results
            formatted_output = self._format_optimization_results(results, args, code)
            self.write_output(formatted_output, args.output)
            
            return {
                "success": True,
                "message": f"Optimization completed for {args.type} optimization",
                "results": results
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _perform_optimization(self, code: str, opt_type: str, target: str,
                                   aggressive: bool, safe_only: bool,
                                   orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Perform the specified optimization."""
        results = {}

        # Try to use advanced capabilities if available
        if hasattr(orchestrator, 'code_analyzer'):
            advanced_orchestrator = orchestrator
        else:
            # Create advanced orchestrator for better optimization
            capabilities = AgentCapabilities(
                enable_advanced_code_generation=True,
                enable_automatic_evaluation=True,
                enable_comprehensive_testing=True
            )
            advanced_orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

        if opt_type in ['performance', 'all']:
            if hasattr(advanced_orchestrator, 'code_analyzer'):
                # Use advanced analysis for optimization
                context = await advanced_orchestrator.code_analyzer.analyze_code_comprehensive(
                    code, "temp_optimization.py", include_relationships=True
                )

                # Generate optimization suggestions based on analysis
                optimization_suggestions = []
                for issue in context.potential_issues:
                    if issue.get('type') in ['performance', 'complexity']:
                        optimization_suggestions.append({
                            'type': 'performance',
                            'description': issue.get('message', ''),
                            'suggestion': issue.get('suggestion', ''),
                            'line': issue.get('line', 0),
                            'severity': issue.get('severity', 'medium')
                        })

                perf_result = {
                    'success': True,
                    'result': {
                        'optimizations': optimization_suggestions,
                        'complexity_metrics': context.complexity_metrics,
                        'target_metric': target,
                        'aggressive_mode': aggressive,
                        'safe_only': safe_only
                    }
                }
            else:
                # Fallback to plugin system
                perf_result = await self.handle_plugin_request(
                    orchestrator,
                    'code_optimization',
                    'optimize_performance',
                    {
                        'code': code,
                        'target_metric': target
                    }
                )
            results['performance'] = perf_result
        
        if opt_type in ['memory', 'all']:
            memory_result = await self.handle_plugin_request(
                orchestrator,
                'code_optimization',
                'optimize_memory',
                {'code': code}
            )
            results['memory'] = memory_result
        
        if opt_type in ['algorithms', 'all']:
            algo_result = await self.handle_plugin_request(
                orchestrator,
                'code_optimization',
                'suggest_algorithms',
                {
                    'code': code,
                    'problem_type': 'general'
                }
            )
            results['algorithms'] = algo_result
        
        if opt_type in ['bottlenecks', 'all']:
            bottleneck_result = await self.handle_plugin_request(
                orchestrator,
                'code_optimization',
                'identify_bottlenecks',
                {'code': code}
            )
            results['bottlenecks'] = bottleneck_result
        
        if opt_type in ['refactor', 'all']:
            refactor_result = await self.handle_plugin_request(
                orchestrator,
                'code_optimization',
                'refactor_for_efficiency',
                {
                    'code': code,
                    'refactoring_type': 'all'
                }
            )
            results['refactor'] = refactor_result
        
        # Add optimization summary
        results['summary'] = self._generate_optimization_summary(results)
        
        return results
    
    def _generate_optimization_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimization summary."""
        summary = {
            "total_optimizations": 0,
            "estimated_improvement": 0.0,
            "priority_fixes": [],
            "applied_optimizations": []
        }
        
        for opt_type, result in results.items():
            if opt_type == 'summary' or not result.get('success'):
                continue
            
            data = result.get('result', {})
            
            if opt_type == 'performance':
                optimizations = data.get('optimizations', [])
                summary["total_optimizations"] += len(optimizations)
                summary["estimated_improvement"] += data.get('performance_gain', 0)
                
                for opt in optimizations:
                    if opt.get('priority') == 'high':
                        summary["priority_fixes"].append(opt.get('message', ''))
                    summary["applied_optimizations"].append({
                        "type": opt.get('type', ''),
                        "message": opt.get('message', ''),
                        "gain": opt.get('estimated_gain', 0)
                    })
            
            elif opt_type == 'memory':
                optimizations = data.get('optimizations', [])
                summary["total_optimizations"] += len(optimizations)
                
                for opt in optimizations:
                    summary["applied_optimizations"].append({
                        "type": "memory",
                        "message": opt.get('message', ''),
                        "saving": opt.get('memory_saving', 0)
                    })
            
            elif opt_type == 'bottlenecks':
                bottlenecks = data.get('bottlenecks', [])
                for bottleneck in bottlenecks:
                    if bottleneck.get('severity') == 'high':
                        summary["priority_fixes"].append(bottleneck.get('message', ''))
        
        return summary
    
    def _format_optimization_results(self, results: Dict[str, Any], 
                                    args: argparse.Namespace, 
                                    original_code: str) -> str:
        """Format optimization results for output."""
        if args.format == 'json':
            return self.format_output(results, 'json', args.pretty)
        elif args.format == 'yaml':
            return self.format_output(results, 'yaml', args.pretty)
        else:
            return self._format_as_text(results, args, original_code)
    
    def _format_as_text(self, results: Dict[str, Any], 
                       args: argparse.Namespace, original_code: str) -> str:
        """Format results as human-readable text."""
        lines = []
        lines.append("Code Optimization Results")
        lines.append("=" * 50)
        
        # Summary
        if 'summary' in results:
            summary = results['summary']
            lines.append(f"\nTotal Optimizations Found: {summary.get('total_optimizations', 0)}")
            lines.append(f"Estimated Performance Improvement: {summary.get('estimated_improvement', 0):.1f}%")
            
            if summary.get('priority_fixes'):
                lines.append("\nHigh Priority Fixes:")
                for fix in summary['priority_fixes']:
                    lines.append(f"  • {fix}")
            
            if summary.get('applied_optimizations'):
                lines.append("\nOptimizations Applied:")
                for opt in summary['applied_optimizations']:
                    gain_info = ""
                    if 'gain' in opt:
                        gain_info = f" (gain: {opt['gain']}%)"
                    elif 'saving' in opt:
                        gain_info = f" (saving: {opt['saving']}%)"
                    lines.append(f"  • {opt.get('message', '')}{gain_info}")
        
        # Show optimized code if available
        optimized_code = None
        for opt_type, result in results.items():
            if result.get('success'):
                data = result.get('result', {})
                if 'optimized_code' in data:
                    optimized_code = data['optimized_code']
                    break
                elif 'refactored_code' in data:
                    optimized_code = data['refactored_code']
                    break
        
        if optimized_code and optimized_code != original_code:
            lines.append("\nOptimized Code:")
            lines.append("-" * 30)
            lines.append(optimized_code)
            
            if args.show_diff:
                lines.append("\nDifferences:")
                lines.append("-" * 30)
                diff = self._generate_diff(original_code, optimized_code)
                lines.append(diff)
        
        return "\n".join(lines)
    
    def _generate_diff(self, original: str, optimized: str) -> str:
        """Generate a simple diff between original and optimized code."""
        import difflib
        
        original_lines = original.splitlines(keepends=True)
        optimized_lines = optimized.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            optimized_lines,
            fromfile='original.py',
            tofile='optimized.py',
            lineterm=''
        )
        
        return ''.join(diff)
