"""
Enhanced command for the CLI.

Provides comprehensive code enhancement capabilities using all enhanced features
including automatic bug fixing, evaluation cycles, and robust code generation.
"""

import argparse
from typing import Any, Dict
from pathlib import Path

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from ...core.agent_orchestrator import AdvancedAgentOrchestrator, AgentCapabilities
from .base import (
    AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
    PluginCommandMixin, ValidationMixin
)


class EnhanceCommand(AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
                    PluginCommandMixin, ValidationMixin):
    """Command for comprehensive code enhancement using all enhanced capabilities."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "enhance"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Comprehensively enhance code using all advanced capabilities"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # File input arguments
        self.add_file_arguments(parser)
        
        # Enhancement options
        enhance_group = parser.add_argument_group('Enhancement Options')
        enhance_group.add_argument(
            '--goals', '-g',
            nargs='+',
            choices=['quality', 'performance', 'security', 'maintainability', 'testing', 'documentation'],
            default=['quality'],
            help='Enhancement goals (default: quality)'
        )
        enhance_group.add_argument(
            '--enable-bug-fixing',
            action='store_true',
            help='Enable automatic bug fixing loop'
        )
        enhance_group.add_argument(
            '--enable-evaluation',
            action='store_true',
            help='Enable automatic evaluation cycles'
        )
        enhance_group.add_argument(
            '--max-iterations',
            type=int,
            default=3,
            help='Maximum iterations for bug fixing (default: 3)'
        )
        enhance_group.add_argument(
            '--comprehensive',
            action='store_true',
            help='Run comprehensive enhancement with all features'
        )
        enhance_group.add_argument(
            '--output-file',
            type=str,
            help='Output file for enhanced code'
        )
        
        # Plugin arguments
        self.add_plugin_arguments(parser)
        
        # Output arguments
        self.add_output_arguments(parser)
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Comprehensively enhance Python code using all advanced capabilities.

Enhancement Goals:
  quality         - Improve code quality and maintainability
  performance     - Optimize for better performance
  security        - Fix security vulnerabilities
  maintainability - Improve code structure and readability
  testing         - Add comprehensive tests
  documentation   - Improve documentation and comments

Examples:
  agent-framework enhance --file code.py --goals quality performance
  agent-framework enhance --code "def hello(): pass" --comprehensive
  agent-framework enhance --file app.py --enable-bug-fixing --enable-evaluation
  agent-framework enhance --stdin --goals security testing --output-file advanced.py
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the enhance command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Get code input
            code = self.get_code_input(args)
            
            # Validate code
            if not self.validate_python_code(code):
                return {
                    "success": False,
                    "error": "Invalid Python code syntax"
                }
            
            # Determine file path
            file_path = args.file if args.file else "advanced_code.py"
            
            # Configure agent capabilities
            capabilities = AgentCapabilities(
                enable_advanced_code_generation=True,
                enable_comprehensive_testing='testing' in args.goals,
                enable_automatic_bug_fixing=args.enable_bug_fixing or args.comprehensive,
                enable_automatic_evaluation=args.enable_evaluation or args.comprehensive,
                max_fix_iterations=args.max_iterations,
                evaluation_on_every_change=args.comprehensive,
                rollback_on_critical_issues=True
            )
            
            # Create advanced orchestrator
            advanced_orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
            
            # Perform comprehensive enhancement
            results = await self._perform_enhancement(
                code, file_path, args.goals, advanced_orchestrator
            )
            
            # Save enhanced code if output file specified
            if args.output_file and results.get('success'):
                enhanced_code = results.get('result', {}).get('enhanced_code', '')
                if enhanced_code:
                    output_path = Path(args.output_file)
                    output_path.write_text(enhanced_code, encoding='utf-8')
                    results['output_file'] = str(output_path)
            
            # Format and return results
            if args.format in ['json', 'yaml']:
                results['formatted_output'] = self.format_output(results, args.format, args.pretty)
            
            return results
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _perform_enhancement(self, code: str, file_path: str, goals: list,
                                  advanced_orchestrator: AdvancedAgentOrchestrator) -> Dict[str, Any]:
        """Perform comprehensive code enhancement."""
        try:
            # Define enhancement requirements
            requirements = {
                "type": "enhancement",
                "goals": goals,
                "description": f"Comprehensive enhancement focusing on: {', '.join(goals)}"
            }
            
            # Run full enhancement cycle
            results = await advanced_orchestrator.run_full_enhancement_cycle(
                code_content=code,
                file_path=file_path,
                requirements=requirements
            )
            
            return {
                "success": True,
                "message": "Code enhancement completed successfully",
                "result": results
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Enhancement failed: {str(e)}"
            }
