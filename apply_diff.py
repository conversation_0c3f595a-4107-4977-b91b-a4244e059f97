import os
import re
import shutil
import tempfile
import difflib
from pathlib import Path
from typing import Union, List, Dict, Optional, Any
import logging

# You must install the 'patch' library: pip install patch
try:
    import patch
except ImportError:
    print("Error: The 'patch' library is required for apply_patch().")
    print("Please install it using: pip install patch")
    exit(1)

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Custom Exceptions ---

class ContentNotFoundError(ValueError):
    """Raised when the content to be replaced is not found in the file."""
    pass

class ContentFoundMultipleTimesError(ValueError):
    """Raised when content is found more times than expected."""
    pass

class PatchApplyError(RuntimeError):
    """Raised when a patch file fails to apply correctly."""
    pass

class PatchValidationError(ValueError):
    """Raised when a patch is malformed or cannot be parsed."""
    pass

class FileOperationError(OSError):
    """Raised for file permission or I/O errors."""
    pass

# Simple match object for fuzzy matching
class SimpleMatch:
    def __init__(self, start: int, end: int):
        self._start = start
        self._end = end

    def span(self):
        return (self._start, self._end)

# --- Utility: Apply Inline Diff (Enhanced Line Replacement) ---

def apply_diff(
    source_file_path: Union[Path, str],
    old_content: str,
    new_content: str,
    *,
    exact_match: bool = True,
    count: int = 1,  # Number of replacements expected
    backup_extension: Union[str, None] = ".bak",
    dry_run: bool = False,
    strip: bool = False,  # Strip whitespace before matching
) -> Union[str, None]:
    """
    Replace a block of text in a file using simple string search-and-replace.
    Useful for small, predictable changes when you have the exact old/new content.
    """
    source_path = Path(source_file_path)
    if not source_path.is_file():
        raise FileNotFoundError(f"Source file not found: {source_path}")

    try:
        content = source_path.read_text(encoding="utf-8")
    except IOError as e:
        raise FileOperationError(f"Could not read file {source_path}: {e}")

    matches = []

    if exact_match:
        matches = list(re.finditer(re.escape(old_content), content))
    else:
        # Fuzzy matching using difflib
        old_lines = old_content.splitlines(keepends=True)
        file_lines = content.splitlines(keepends=True)

        if strip:
            stripped_old_lines = [line.strip() for line in old_lines]
            stripped_file_lines = [line.strip() for line in file_lines]
            matcher = difflib.SequenceMatcher(None, stripped_old_lines, stripped_file_lines)
        else:
            matcher = difflib.SequenceMatcher(None, old_lines, file_lines)

        # Find fuzzy matches
        for block in matcher.get_matching_blocks():
            if block.size > 0:
                if old_lines:
                    first_old_line = old_lines[0].strip() if strip else old_lines[0]
                    for i, file_line in enumerate(file_lines):
                        file_line_cmp = file_line.strip() if strip else file_line
                        if first_old_line in file_line_cmp or difflib.SequenceMatcher(None, first_old_line, file_line_cmp).ratio() > 0.8:
                            match_len = 0
                            j = i
                            for old_line in old_lines:
                                if j < len(file_lines):
                                    file_line_cmp = file_lines[j].strip() if strip else file_lines[j]
                                    old_line_cmp = old_line.strip() if strip else old_line
                                    if old_line_cmp == file_line_cmp or difflib.SequenceMatcher(None, old_line_cmp, file_line_cmp).ratio() > 0.8:
                                        match_len += 1
                                        j += 1
                                    else:
                                        break
                            if match_len == len(old_lines):
                                match_start_pos = sum(len(l) for l in file_lines[:i])
                                match_end_pos = sum(len(l) for l in file_lines[:j])
                                matches.append(SimpleMatch(match_start_pos, match_end_pos))
                                if count != 0:
                                    break

    num_matches = len(matches)

    if num_matches == 0:
        raise ContentNotFoundError(f"Content to replace not found in {source_path}")

    if count > 0 and num_matches != count:
        raise ContentFoundMultipleTimesError(
            f"Expected {count} match(es), found {num_matches} for replacement in {source_path}"
        )

    # Apply replacements in reverse order to keep indices valid
    result_content = content
    for match in reversed(matches):
        start, end = match.span()
        result_content = result_content[:start] + new_content + result_content[end:]

    if dry_run:
        return result_content

    # Backup
    backup_path = None
    if backup_extension:
        backup_path = source_path.with_suffix(source_path.suffix + backup_extension)
        try:
            shutil.copy2(source_path, backup_path)
        except IOError as e:
            raise FileOperationError(f"Could not create backup {backup_path}: {e}")

    # Write result
    try:
        source_path.write_text(result_content, encoding="utf-8")
        if backup_path:
            st = os.stat(backup_path)
            os.chmod(source_path, st.st_mode)
    except IOError as e:
        raise FileOperationError(f"Could not write to file {source_path}: {e}")

    return None

# --- Main: Apply Patch (Enhanced) ---

def apply_patch(
    source_file_path: Union[Path, str],
    patch_source: Union[Path, str],
    *,
    backup_extension: Union[str, None] = ".bak",
    dry_run: bool = False,
    verbose: bool = False,
    reverse: bool = False,
    fuzz: bool = False,
    strip_leading_dirs: bool = False,
    ignore_whitespace: bool = False,
    force: bool = False,
) -> Union[str, None]:
    """
    Applies a standard unified diff patch to a source file.
    """
    source_path = Path(source_file_path)
    if not source_path.is_file():
        raise FileNotFoundError(f"Source file not found: {source_path}")

    # Check read/write permissions
    if not os.access(source_path, os.R_OK):
        raise FileOperationError(f"Permission denied reading {source_path}")
    if not dry_run and not os.access(source_path.parent, os.W_OK):
        raise FileOperationError(f"Permission denied writing to {source_path.parent}")

    # Load patch
    patch_set = None
    patch_data_str = ""
    try:
        if isinstance(patch_source, (str, Path)) and Path(patch_source).is_file():
            patch_file = Path(patch_source)
            patch_set = patch.fromfile(str(patch_file))
            patch_data_str = patch_file.read_text(encoding='utf-8')
        elif isinstance(patch_source, str):
            patch_set = patch.fromstring(patch_source.encode("utf-8"))
            patch_data_str = patch_source
        else:
            raise ValueError("`patch_source` must be a valid file path or a string of patch content.")
    except Exception as e:
        raise PatchValidationError(f"Failed to parse patch: {e}")

    # Check if patch_set is valid and has items
    if not patch_set or len(patch_set) == 0:
        raise PatchValidationError("Patch is empty or invalid.")
    
    # Check if any patch items have hunks
    has_valid_hunks = any(len(p.hunks) > 0 for p in patch_set if hasattr(p, 'hunks'))
    if not has_valid_hunks:
        raise PatchValidationError("No valid hunks found in patch.")

    if strip_leading_dirs or ignore_whitespace:
        processed_patch_lines = patch_data_str.splitlines(keepends=True)
        if strip_leading_dirs:
            new_lines = []
            for line in processed_patch_lines:
                if line.startswith("--- a/"):
                    line = "--- " + line[6:]
                elif line.startswith("+++ b/"):
                    line = "+++ " + line[6:]
                new_lines.append(line)
            processed_patch_lines = new_lines
        if ignore_whitespace:
            logger.warning("ignore_whitespace flag might require patch library support or manual preprocessing.")

        try:
            patch_set = patch.fromstring("".join(processed_patch_lines).encode("utf-8"))
            if not patch_set or len(patch_set) == 0:
                raise PatchValidationError("Processed patch became invalid.")
        except Exception as e:
            raise PatchValidationError(f"Failed to re-parse processed patch: {e}")

    if verbose:
        logger.info(f"Loaded patch with {len(patch_set)} file sections.")

    # Dry run: simulate
    if dry_run:
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_source_path = Path(tmpdir) / source_path.name
            try:
                shutil.copy2(source_path, tmp_source_path)
            except IOError as e:
                raise FileOperationError(f"Could not copy source to temp dir: {e}")

            if ignore_whitespace:
                pass

            success = patch_set.apply(root=tmpdir)
            if not success:
                raise PatchApplyError("Dry run failed: patch cannot be applied cleanly.")
            try:
                return tmp_source_path.read_text(encoding="utf-8")
            except IOError as e:
                raise FileOperationError(f"Could not read result from temp dir: {e}")

    # Real run
    backup_path = None
    if backup_extension:
        backup_path = source_path.with_suffix(source_path.suffix + backup_extension)
        try:
            shutil.copy2(source_path, backup_path)  # Preserve metadata
        except IOError as e:
            raise FileOperationError(f"Could not create backup {backup_path}: {e}")

    # Preprocess target file for ignore_whitespace if needed (complex, simplified)
    if ignore_whitespace:
        # This is complex. One way is to create a temporary copy with normalized whitespace.
        # This is a conceptual placeholder.
        logger.warning(
            "ignore_whitespace for real run requires complex file preprocessing. Proceeding with standard apply.")

    # Apply patch to actual file - remove unsupported parameters
    success = patch_set.apply(root=source_path.parent)

    if not success:
        # Look for .rej files
        # This might not find them correctly if patch lib uses diff names
        rej_files = list(source_path.parent.glob("*.rej"))
        rej_msg = f" Check {len(rej_files)} .rej file(s) generated." if rej_files else ""
        raise PatchApplyError(
            f"Patch failed to apply to {source_path}.{rej_msg} "
            f"Consider using 'fuzz=True' or checking file/patch compatibility."
        )

    # Restore original file permissions and metadata after patch modifies them
    # (patch lib might change them)
    if backup_path:
        try:
            st = os.stat(backup_path)
            os.chmod(source_path, st.st_mode)
            # shutil.copystat(backup_path, source_path) # Copy all metadata if needed
        except OSError as e:
            logger.warning(f"Could not fully restore metadata to {source_path}: {e}")

    return None

# --- New: Create Patch from Two Files (Enhanced) ---

def create_patch(
    original_file: Union[Path, str],
    modified_file: Union[Path, str],
    *,
    output: Union[Path, str, None] = None,
    header_a: str = "a/",
    header_b: str = "b/",
    context_lines: int = 3,  # New
    ignore_whitespace: bool = False,  # New
) -> str:
    """
    Generate a unified diff patch between two files.
    Args:
        original_file: Original version.
        modified_file: Modified version.
        output: Optional path to save patch.
        header_a: Prefix for original file (e.g., 'a/')
        header_b: Prefix for modified file (e.g., 'b/')
        context_lines: Number of context lines (default 3).
        ignore_whitespace: Generate diff ignoring whitespace changes.
    Returns:
        Patch content as string.
    """
    orig = Path(original_file)
    mod = Path(modified_file)
    if not orig.is_file() or not mod.is_file():
        raise FileNotFoundError("Both files must exist.")

    try:
        with open(orig, 'r', encoding='utf-8') as f:
            a_lines = f.readlines()
        with open(mod, 'r', encoding='utf-8') as f:
            b_lines = f.readlines()
    except IOError as e:
        raise FileOperationError(f"Error reading files: {e}")

    rel_orig = header_a + orig.name
    rel_mod = header_b + mod.name

    # Select diff function based on ignore_whitespace
    if ignore_whitespace:
        # difflib does not have a direct ignore_whitespace flag.
        # We need to preprocess the lines.
        def normalize_ws(line):
            # Join with single space, ensure newline
            return ' '.join(line.split()) + '\n'
        norm_a_lines = [normalize_ws(line) for line in a_lines]
        norm_b_lines = [normalize_ws(line) for line in b_lines]
        diff = difflib.unified_diff(
            norm_a_lines, norm_b_lines,
            fromfile=rel_orig,
            tofile=rel_mod,
            n=context_lines
        )
        # Note: The output diff lines will reflect the normalized whitespace.
    else:
        diff = difflib.unified_diff(
            a_lines, b_lines,
            fromfile=rel_orig,
            tofile=rel_mod,
            n=context_lines
        )

    patch_content = ''.join(diff)
    if output:
        try:
            Path(output).write_text(patch_content, encoding="utf-8")
        except IOError as e:
            raise FileOperationError(f"Could not write patch to {output}: {e}")
    return patch_content

# --- New: List Hunks in Patch (Enhanced) ---

def list_patch_hunks(patch_source: Union[Path, str]) -> List[Dict[str, Any]]:
    """
    Parse and list hunks in a patch without applying.
    Returns:
        List of dicts with: 'file', 'hunk_header', 'content', 'old_start', 'old_count', 'new_start', 'new_count'
    """
    patch_set = None
    try:
        if isinstance(patch_source, (str, Path)) and Path(patch_source).is_file():
            patch_set = patch.fromfile(str(patch_source))
        elif isinstance(patch_source, str):
            patch_set = patch.fromstring(patch_source.encode("utf-8"))
        else:
            raise ValueError("Invalid patch source.")
    except Exception as e:
        raise PatchValidationError(
            f"Failed to parse patch for listing hunks: {e}")

    if not patch_set:
        raise PatchValidationError("No valid hunks found in patch.")

    hunks = []
    for p in patch_set:
        target_file = p.target.decode(
            'utf-8') if isinstance(p.target, bytes) else p.target
        # source_file = p.source.decode('utf-8') if isinstance(p.source, bytes) else p.source
        for hunk in p.hunks:
            # Parse hunk header @@ -old_start,old_count +new_start,new_count @@
            header_match = re.match(
                r"@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@ ?", hunk.section.decode('utf-8'))
            old_start, old_count, new_start, new_count = 1, 1, 1, 1  # Defaults
            if header_match:
                old_start = int(header_match.group(1))
                old_count = int(header_match.group(
                    2)) if header_match.group(2) else 1
                new_start = int(header_match.group(3))
                new_count = int(header_match.group(
                    4)) if header_match.group(4) else 1

            hunks.append({
                "file": target_file,
                "hunk_header": hunk.section.decode('utf-8').strip(),
                "content": hunk.text.decode('utf-8') if isinstance(hunk.text, bytes) else hunk.text,
                "old_start": old_start,
                "old_count": old_count,
                "new_start": new_start,
                "new_count": new_count,
            })
    return hunks

# --- New: Reverse a Patch (Semantically Clear Alias) ---

def reverse_patch(
    source_file_path: Union[Path, str],
    patch_source: Union[Path, str],
    *,
    backup_extension: Union[str, None] = ".bak.reversed",
    dry_run: bool = False,
    verbose: bool = False,
    force: bool = False,  # New
) -> Union[str, None]:
    """
    Apply a patch in reverse (undo changes).
    Same as `apply_patch(..., reverse=True)`, but more semantically clear.
    """
    return apply_patch(
        source_file_path=source_file_path,
        patch_source=patch_source,
        backup_extension=backup_extension,
        dry_run=dry_run,
        reverse=True,
        verbose=verbose,
        force=force
    )

# --- New: Get File Status Relative to Patch ---

def get_file_status(
    source_file_path: Union[Path, str],
    patch_source: Union[Path, str]
) -> str:
    """
    Determine if a patch is already applied, reversed, or partially applied.
    Note: This is a simplified heuristic. A full implementation is complex.
    Returns:
        'applied', 'reversed', 'partial', 'unknown'
    """
    # This is a conceptual implementation.
    # A robust version would need to parse the patch and check the exact state
    # of lines in the file.
    try:
        # Try applying the patch in dry-run mode
        apply_patch(source_file_path, patch_source, dry_run=True)
        # If it applies cleanly, it's likely 'reversed' (original state)
        return 'reversed'
    except PatchApplyError:
        try:
            # Try applying the patch in reverse dry-run mode
            apply_patch(source_file_path, patch_source,
                        dry_run=True, reverse=True)
            # If the reverse applies cleanly, it's likely 'applied'
            return 'applied'
        except PatchApplyError:
            # Neither applies cleanly
            return 'partial'  # Or 'unknown'
    except Exception:
        return 'unknown'

# --- New: Find Patch Hunk Location in File ---

def find_patch_in_file(
    source_file_path: Union[Path, str],
    patch_hunk_content: str  # The content part of a single hunk
) -> Optional[int]:
    """
    Find the approximate starting line number in a file where a patch hunk would apply.
    This is a simplified version using string search on hunk content.
    Args:
        source_file_path: Path to the file.
        patch_hunk_content: String content of the hunk (including @@ header).
    Returns:
        Line number (1-based) if found, else None.
    """
    source_path = Path(source_file_path)
    if not source_path.is_file():
        raise FileNotFoundError(f"Source file not found: {source_path}")

    try:
        content = source_path.read_text(encoding="utf-8")
    except IOError as e:
        raise FileOperationError(f"Could not read file {source_path}: {e}")

    content.splitlines(keepends=True)
    hunk_lines = patch_hunk_content.splitlines(keepends=True)

    if not hunk_lines:
        return None

    # Extract the "context" lines (those starting with ' ' or '-') from the hunk
    # This is a simplified heuristic
    context_lines = [line[1:] for line in hunk_lines if line.startswith(
        ' ') or line.startswith('-')]
    if not context_lines:
        return None  # No context to search for

    # Search for the first few context lines in the file
    # This is very basic and might not work for complex cases
    # First 3 context lines
    search_snippet = "".join(context_lines[:min(3, len(context_lines))])
    try:
        start_pos = content.index(search_snippet)
        # Calculate line number
        line_num = content.count('\n', 0, start_pos) + 1
        return line_num
    except ValueError:
        # Snippet not found
        return None


# --- Example Usage (Updated and Extended) ---
if __name__ == "__main__":
    # === Setup for the Example ===
    source_code_path = Path("user_service.py")
    backup_path = source_code_path.with_suffix(
        source_code_path.suffix + ".bak")
    source_code_content = """# user_service.py
def get_user(user_id):
    \"\"\"Fetches a user from the database.\"\"\"
    if not user_id:
        return None
    # DB logic here
    print(f"Fetching user {user_id}")
    return {"id": user_id, "name": "John Doe"}
def delete_user(user_id):
    # TODO: Implement user deletion
    pass
"""

    try:
        source_code_path.write_text(source_code_content, encoding="utf-8")
    except IOError as e:
        print(f"❌ Setup failed: Could not write {source_code_path}: {e}")
        exit(1)

    # Patch content
    patch_file_path = Path("0001-improve-user-service.patch")
    patch_content = """--- a/user_service.py
+++ b/user_service.py
@@ -1,8 +1,11 @@
 # user_service.py
 def get_user(user_id):
     \"\"\"Fetches a user from the database.\"\"\"
-    if not user_id:
+    if not isinstance(user_id, int) or user_id <= 0:
+        print("Error: Invalid user_id provided.")
         return None
     # DB logic here
     print(f"Fetching user {user_id}")
     return {"id": user_id, "name": "John Doe"}
 def delete_user(user_id):
-    # TODO: Implement user deletion
-    pass
+    \"\"\"Deletes a user.\"\"\"
+    print(f"Deleting user {user_id}")
+    return True
"""

    try:
        patch_file_path.write_text(patch_content, encoding="utf-8")
    except IOError as e:
        print(f"❌ Setup failed: Could not write {patch_file_path}: {e}")
        exit(1)

    print(
        f"--- Created '{source_code_path}' and '{patch_file_path}' for testing ---")

    # --- 1. Dry Run ---
    print("\n1. Testing DRY RUN...")
    try:
        result = apply_patch(source_code_path, patch_content,
                             dry_run=True, verbose=True)
        print("✅ Dry run successful. Preview:")
        # print(result) # Can be verbose
    except Exception as e:
        print(f"❌ Failed: {e}")

    # --- 2. Apply Patch ---
    print("\n2. Applying patch...")
    try:
        apply_patch(source_code_path, patch_file_path, backup_extension=".bak")
        print(f"✅ Patch applied. Backup at '{backup_path}'")
    except Exception as e:
        print(f"❌ Failed: {e}")

    # --- 3. List Hunks ---
    print("\n3. Inspecting patch hunks...")
    try:
        hunks = list_patch_hunks(patch_file_path)
        for h in hunks:
            print(f"  File: {h['file']}")
            print(f"    Header: {h['hunk_header']}")
            print(
                f"    Old: {h['old_start']},{h['old_count']} -> New: {h['new_start']},{h['new_count']}")
            # print(f"    Content Preview: {h['content'][:100]}...")
    except Exception as e:
        print(f"❌ Failed: {e}")

    # --- 4. Create Patch from Files ---
    print("\n4. Creating a new patch from diff...")
    try:
        modified_path = Path("user_service_v2.py")
        modified_content = source_code_path.read_text(
        ) + "\ndef list_users():\n    return []\n"
        modified_path.write_text(modified_content, encoding="utf-8")

        new_patch = create_patch(
            source_code_path, modified_path, output="add_list_users.patch", context_lines=5)
        print("✅ Created new patch with 5 context lines:")
        print(new_patch)
        modified_path.unlink(missing_ok=True)
        Path("add_list_users.patch").unlink(missing_ok=True)
    except Exception as e:
        print(f"❌ Failed: {e}")

    # --- 5. Reverse Patch ---
    print("\n5. Reversing the patch...")
    try:
        reverse_patch(source_code_path, patch_file_path,
                      backup_extension=".bak.reverse")
        print("✅ Patch reversed. Original logic restored.")
        print("Final content:")
        print(source_code_path.read_text())
    except PatchApplyError as e:
        print(
            f"✅ Correctly failed to reverse (might already be reversed): {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

    # --- 6. Test apply_diff with new features ---
    print("\n6. Testing enhanced apply_diff...")
    test_file = Path("test_diff.txt")
    test_content = "Line 1\nLine 2\n  Line 3  \nLine 4\nLine 2\n"
    test_file.write_text(test_content, encoding='utf-8')

    # Test exact match count
    try:
        apply_diff(test_file, "Line 2", "Line TWO", count=2,
                   backup_extension=".bak_diff")  # Should work
        print("✅ apply_diff with count=2 succeeded.")
        print("Result:", test_file.read_text())
    except Exception as e:
        print(f"❌ apply_diff count test failed: {e}")

    test_file.unlink(missing_ok=True)
    test_file.with_suffix(
        test_file.suffix + ".bak_diff").unlink(missing_ok=True)

    # --- 7. Test get_file_status ---
    print("\n7. Testing get_file_status...")
    # Recreate original file
    source_code_path.write_text(source_code_content, encoding="utf-8")
    status_reversed = get_file_status(source_code_path, patch_file_path)
    print(f"Status (original file): {status_reversed}")  # Should be 'reversed'

    # Apply patch again
    apply_patch(source_code_path, patch_file_path)
    status_applied = get_file_status(source_code_path, patch_file_path)
    print(f"Status (patched file): {status_applied}")  # Should be 'applied'

    # --- 8. Test find_patch_in_file ---
    print("\n8. Testing find_patch_in_file...")
    # Use the patched file and the hunk content
    source_code_path.write_text(source_code_content, encoding="utf-8")  # Reset
    apply_patch(source_code_path, patch_file_path)  # Apply again
    hunks = list_patch_hunks(patch_file_path)
    if hunks:
        hunk_content = hunks[0]['content']
        line_num = find_patch_in_file(source_code_path, hunk_content)
        print(f"Found hunk start at approximate line: {line_num}")

    # --- Cleanup ---
    for p in [source_code_path, patch_file_path, backup_path]:
        p.unlink(missing_ok=True)
    reverse_backup = source_code_path.with_suffix(
        source_code_path.suffix + ".bak.reverse")
    reverse_backup.unlink(missing_ok=True)
    test_file.unlink(missing_ok=True)
    test_file.with_suffix(
        test_file.suffix + ".bak_diff").unlink(missing_ok=True)

    print(f"\n--- Cleaned up test files ---")
