#!/usr/bin/env python3

from agent_framework.core.config import ModelConfig, ModelProvider

def test_config():
    print("Testing ModelConfig validation...")
    
    # Create config with OPENAI provider
    config = ModelConfig(provider=ModelProvider.OPENAI, model="gpt-4")
    print(f"Initial config: provider={config.provider}, base_url={config.base_url}")
    
    # Trigger validation
    validated_config = ModelConfig.parse_obj(config.dict())
    print(f"Validated config: provider={validated_config.provider}, base_url={validated_config.base_url}")
    
    print(f"Expected: https://api.openai.com/v1")
    print(f"Actual: {validated_config.base_url}")
    print(f"Match: {validated_config.base_url == 'https://api.openai.com/v1'}")

if __name__ == "__main__":
    test_config()
