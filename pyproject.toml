[project]
name = "agent-test"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "autogen-agentchat>=0.7.1",
    "autogen-ext[diskcache,docker,mcp,openai]>=0.7.1",
    "colorama>=0.4.6",
    "docker>=7.1.0",
    "mcp-server-fetch>=2025.4.7",
    "patch>=1.16",
    "psutil>=5.9.0",
    "pydantic>=2.0.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pyyaml>=6.0.0",
]

[project.scripts]
agent-framework = "agent_framework.cli.core:main"

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
