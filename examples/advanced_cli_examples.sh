#!/bin/bash
# Advanced CLI Examples for the AI Agent Framework
# 
# This script demonstrates advanced usage patterns and real-world scenarios
# using the command-line interface of the AI agent framework.

echo "🚀 Advanced CLI Examples for AI Agent Framework"
echo "================================================"

# Set up common variables
SAMPLE_FILE="examples/sample_code.py"
OUTPUT_DIR="output"
mkdir -p "$OUTPUT_DIR"

# Create a sample file for demonstrations
cat > "$SAMPLE_FILE" << 'EOF'
import requests
import json

def fetch_user_data(user_id):
    url = f"https://api.example.com/users/{user_id}"
    response = requests.get(url)
    return json.loads(response.text)

def process_users(user_ids):
    results = []
    for uid in user_ids:
        try:
            user = fetch_user_data(uid)
            results.append(user)
        except:
            pass
    return results

class DataProcessor:
    def __init__(self):
        self.cache = {}
    
    def process(self, data):
        if data in self.cache:
            return self.cache[data]
        
        result = data * 2 + 1
        self.cache[data] = result
        return result
EOF

echo "📝 Created sample file: $SAMPLE_FILE"

echo ""
echo "="*60
echo "EXAMPLE 1: Comprehensive Code Analysis"
echo "="*60

echo "🔍 Basic analysis:"
echo "python -m agent_framework.cli.main analyze --file $SAMPLE_FILE --type basic"

echo ""
echo "🔍 Comprehensive analysis with all metrics:"
echo "python -m agent_framework.cli.main analyze --file $SAMPLE_FILE --type comprehensive --output-file $OUTPUT_DIR/analysis_report.json"

echo ""
echo "🔍 Security-focused analysis:"
echo "python -m agent_framework.cli.main analyze --file $SAMPLE_FILE --type security --verbose"

echo ""
echo "="*60
echo "EXAMPLE 2: Advanced Code Enhancement"
echo "="*60

echo "✨ Basic enhancement for code quality:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --goals quality maintainability"

echo ""
echo "✨ Comprehensive enhancement with all features:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --comprehensive --output-file $OUTPUT_DIR/enhanced_code.py"

echo ""
echo "✨ Security-focused enhancement:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --goals security error_handling --enable-evaluation"

echo ""
echo "✨ Performance optimization:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --goals performance async_support --enable-bug-fixing"

echo ""
echo "="*60
echo "EXAMPLE 3: Intelligent Debugging"
echo "="*60

echo "🐛 Basic debugging assistance:"
echo "python -m agent_framework.cli.main debug --file $SAMPLE_FILE"

echo ""
echo "🐛 Auto-fix with limited iterations:"
echo "python -m agent_framework.cli.main debug --file $SAMPLE_FILE --auto-fix --max-iterations 3"

echo ""
echo "🐛 Comprehensive debugging with detailed analysis:"
echo "python -m agent_framework.cli.main debug --file $SAMPLE_FILE --comprehensive --output-file $OUTPUT_DIR/debug_report.json"

echo ""
echo "="*60
echo "EXAMPLE 4: Advanced Code Generation"
echo "="*60

echo "🏗️  Generate a simple function:"
echo "python -m agent_framework.cli.main generate --type function --name 'validate_email' --description 'Validate email address format'"

echo ""
echo "🏗️  Generate a comprehensive class:"
echo "python -m agent_framework.cli.main generate --type class --name 'UserManager' --comprehensive --output-file $OUTPUT_DIR/user_manager.py"

echo ""
echo "🏗️  Generate API endpoints:"
echo "python -m agent_framework.cli.main generate --type api --name 'UserAPI' --framework fastapi --output-file $OUTPUT_DIR/user_api.py"

echo ""
echo "🏗️  Generate test suite:"
echo "python -m agent_framework.cli.main generate --type tests --file $SAMPLE_FILE --comprehensive --output-file $OUTPUT_DIR/test_sample_code.py"

echo ""
echo "="*60
echo "EXAMPLE 5: Batch Processing and Workflows"
echo "="*60

echo "📁 Process entire directory:"
echo "python -m agent_framework.cli.main analyze --directory src/ --type comprehensive --recursive"

echo ""
echo "📁 Enhance all Python files in a project:"
echo "find . -name '*.py' -not -path './venv/*' | xargs -I {} python -m agent_framework.cli.main enhance --file {} --goals quality"

echo ""
echo "📁 Generate documentation for all modules:"
echo "python -m agent_framework.cli.main generate --type documentation --directory src/ --output-dir $OUTPUT_DIR/docs/"

echo ""
echo "="*60
echo "EXAMPLE 6: Pipeline Integration"
echo "="*60

echo "🔄 CI/CD Integration - Quality Gate:"
cat << 'EOF'
# In your CI/CD pipeline (e.g., .github/workflows/quality.yml):
- name: Code Quality Analysis
  run: |
    python -m agent_framework.cli.main analyze \
      --directory src/ \
      --type comprehensive \
      --output-file quality_report.json \
      --fail-on-critical
    
    # Check if quality score meets threshold
    python -c "
    import json
    with open('quality_report.json') as f:
        report = json.load(f)
    if report['quality_score'] < 7.0:
        exit(1)
    "
EOF

echo ""
echo "🔄 Pre-commit Hook Integration:"
cat << 'EOF'
# In .pre-commit-config.yaml:
repos:
  - repo: local
    hooks:
      - id: ai-agent-enhance
        name: AI Agent Code Enhancement
        entry: python -m agent_framework.cli.main enhance
        args: [--goals, quality, security, --enable-evaluation]
        language: system
        files: \.py$
EOF

echo ""
echo "="*60
echo "EXAMPLE 7: Advanced Configuration"
echo "="*60

echo "⚙️  Using custom configuration file:"
cat > "$OUTPUT_DIR/custom_config.yaml" << 'EOF'
framework:
  log_level: DEBUG
  workspace_path: "/path/to/project"

advanced_capabilities:
  enabled: true
  enable_automatic_bug_fixing: true
  enable_automatic_evaluation: true
  enable_advanced_code_generation: true
  max_fix_iterations: 5
  evaluation_on_every_change: true

agents:
  code_analyzer:
    model: "gpt-4"
    temperature: 0.1
  code_editor:
    model: "claude-3-sonnet"
    temperature: 0.2
EOF

echo "python -m agent_framework.cli.main --config $OUTPUT_DIR/custom_config.yaml enhance --file $SAMPLE_FILE --comprehensive"

echo ""
echo "⚙️  Environment-based configuration:"
cat << 'EOF'
# Set environment variables
export AGENT_MODEL="gpt-4"
export AGENT_TEMPERATURE="0.1"
export AGENT_MAX_ITERATIONS="5"
export AGENT_ENABLE_EVALUATION="true"

# Run with environment configuration
python -m agent_framework.cli.main enhance --file sample.py --comprehensive
EOF

echo ""
echo "="*60
echo "EXAMPLE 8: Monitoring and Reporting"
echo "="*60

echo "📊 Generate comprehensive project report:"
echo "python -m agent_framework.cli.main report --directory src/ --output-file $OUTPUT_DIR/project_report.html --format html"

echo ""
echo "📊 Quality trends analysis:"
echo "python -m agent_framework.cli.main analyze --directory src/ --track-trends --output-file $OUTPUT_DIR/quality_trends.json"

echo ""
echo "📊 Performance benchmarking:"
echo "python -m agent_framework.cli.main benchmark --file $SAMPLE_FILE --iterations 10 --output-file $OUTPUT_DIR/performance_report.json"

echo ""
echo "="*60
echo "EXAMPLE 9: Integration with External Tools"
echo "="*60

echo "🔗 Integration with pytest:"
echo "python -m agent_framework.cli.main generate --type tests --file $SAMPLE_FILE | pytest --verbose"

echo ""
echo "🔗 Integration with black formatter:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --goals formatting | black -"

echo ""
echo "🔗 Integration with mypy type checker:"
echo "python -m agent_framework.cli.main enhance --file $SAMPLE_FILE --goals type_hints --output-file temp.py && mypy temp.py"

echo ""
echo "="*60
echo "EXAMPLE 10: Advanced Scripting Patterns"
echo "="*60

echo "🔧 Conditional enhancement based on file size:"
cat << 'EOF'
#!/bin/bash
for file in src/*.py; do
    size=$(wc -l < "$file")
    if [ "$size" -gt 100 ]; then
        echo "Enhancing large file: $file"
        python -m agent_framework.cli.main enhance \
            --file "$file" \
            --goals complexity performance \
            --comprehensive
    else
        echo "Basic enhancement for: $file"
        python -m agent_framework.cli.main enhance \
            --file "$file" \
            --goals quality
    fi
done
EOF

echo ""
echo "🔧 Quality-driven enhancement pipeline:"
cat << 'EOF'
#!/bin/bash
# 1. Analyze code quality
quality_score=$(python -m agent_framework.cli.main analyze \
    --file "$1" \
    --type quality \
    --output-format json | jq '.quality_score')

# 2. Apply appropriate enhancement based on quality
if (( $(echo "$quality_score < 5.0" | bc -l) )); then
    echo "Low quality detected, applying comprehensive enhancement"
    python -m agent_framework.cli.main enhance \
        --file "$1" \
        --comprehensive \
        --enable-bug-fixing \
        --max-iterations 5
elif (( $(echo "$quality_score < 7.0" | bc -l) )); then
    echo "Medium quality, applying targeted improvements"
    python -m agent_framework.cli.main enhance \
        --file "$1" \
        --goals quality maintainability \
        --enable-evaluation
else
    echo "Good quality, applying minor optimizations"
    python -m agent_framework.cli.main enhance \
        --file "$1" \
        --goals performance
fi
EOF

echo ""
echo "✅ Advanced CLI Examples Complete!"
echo ""
echo "📚 Key Takeaways:"
echo "   • Use --comprehensive for full-featured operations"
echo "   • Combine --enable-evaluation and --enable-bug-fixing for robust results"
echo "   • Leverage --output-file for automation and CI/CD integration"
echo "   • Use configuration files for consistent team settings"
echo "   • Integrate with existing tools and workflows"
echo "   • Monitor quality trends over time"
echo ""
echo "🚀 Next Steps:"
echo "   • Try these commands on your own codebase"
echo "   • Customize configurations for your team's needs"
echo "   • Integrate into your CI/CD pipelines"
echo "   • Explore the Python API for programmatic usage"

# Cleanup
rm -f "$SAMPLE_FILE"
echo ""
echo "🧹 Cleaned up sample files"
