"""
Example demonstrating multi-agent collaboration in the agent framework.

This example shows how to:
1. Configure multiple specialized agents
2. Execute tasks using multi-agent coordination
3. Monitor agent interactions and performance
4. Use MCP servers for enhanced capabilities
"""

import asyncio
import logging
from pathlib import Path

from agent_framework import AgentOrchestrator, FrameworkConfig
from agent_framework.core.config import (
    ModelConfig, MultiAgentConfig, AgentRoleConfig, MCPConfig, MCPServerConfig
)
from agent_framework.core.types import Task, TaskPriority
from agent_framework.core.multi_agent_types import AgentCapability


async def main():
    """Main example function."""
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Create configuration with multi-agent support
    config = create_multi_agent_config()
    
    # Initialize the orchestrator
    orchestrator = AgentOrchestrator(config)
    
    try:
        # Initialize the framework
        await orchestrator.initialize()
        logger.info("Framework initialized successfully")
        
        # Example 1: Simple multi-agent task
        await example_code_analysis_task(orchestrator, logger)
        
        # Example 2: Complex workflow with multiple agents
        await example_complex_workflow(orchestrator, logger)
        
        # Example 3: Monitor multi-agent system
        await example_monitoring(orchestrator, logger)
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise
    
    finally:
        # Shutdown the framework
        await orchestrator.shutdown()
        logger.info("Framework shutdown complete")


def create_multi_agent_config() -> FrameworkConfig:
    """Create a configuration with multi-agent support."""
    
    # Model configuration (you'll need to set your API key)
    model_config = ModelConfig(
        provider="openrouter",
        model="qwen/qwen3-coder:free",
        api_key="your-api-key-here",  # Replace with your actual API key
        base_url="https://openrouter.ai/api/v1"
    )
    
    # MCP server configuration
    mcp_config = MCPConfig(
        servers={
            "filesystem": MCPServerConfig(
                command="npx",
                args=["-y", "@modelcontextprotocol/server-filesystem", str(Path.cwd())],
                description="Filesystem access for reading and writing files"
            ),
            "fetch": MCPServerConfig(
                command="uvx",
                args=["mcp-server-fetch"],
                description="Web content fetching capabilities"
            )
        }
    )
    
    # Multi-agent configuration
    multi_agent_config = MultiAgentConfig(
        enabled=True,
        max_agents=5,
        coordination_strategy="capability_based",
        task_delegation_enabled=True,
        result_sharing_enabled=True,
        conflict_resolution_strategy="voting",
        agent_roles={
            "code_analyst": AgentRoleConfig(
                name="code_analyst",
                description="Specialized in code analysis and quality assessment",
                system_message="You are a code analysis expert. Focus on code quality, complexity, and best practices.",
                capabilities=[AgentCapability.CODE_ANALYSIS.value, AgentCapability.ERROR_DETECTION.value],
                model_config=model_config,
                mcp_servers=["filesystem"],
                max_concurrent_tasks=3,
                priority=3
            ),
            "tester": AgentRoleConfig(
                name="tester",
                description="Specialized in test generation and validation",
                system_message="You are a testing expert. Focus on comprehensive test coverage and quality.",
                capabilities=[AgentCapability.TESTING.value],
                model_config=model_config,
                mcp_servers=["filesystem"],
                max_concurrent_tasks=2,
                priority=2
            ),
            "documenter": AgentRoleConfig(
                name="documenter",
                description="Specialized in documentation creation",
                system_message="You are a documentation expert. Create clear, comprehensive documentation.",
                capabilities=[AgentCapability.DOCUMENTATION.value],
                model_config=model_config,
                mcp_servers=["filesystem", "fetch"],
                max_concurrent_tasks=2,
                priority=1
            )
        }
    )
    
    return FrameworkConfig(
        name="Multi-Agent Example Framework",
        debug=True,
        model=model_config,
        mcp=mcp_config,
        multi_agent=multi_agent_config
    )


async def example_code_analysis_task(orchestrator: AgentOrchestrator, logger: logging.Logger):
    """Example of a code analysis task using multiple agents."""
    logger.info("=== Example 1: Code Analysis Task ===")
    
    # Create a task that requires code analysis
    task = Task(
        name="analyze_python_file",
        description="Analyze a Python file for quality, complexity, and potential issues",
        task_type="code_analysis",
        priority=TaskPriority.HIGH,
        parameters={
            "file_path": "examples/multi_agent_example.py",
            "analysis_types": ["quality", "complexity", "security", "style"]
        }
    )
    
    # Execute using multi-agent coordination
    result = await orchestrator.execute_multi_agent_task(task)
    
    logger.info(f"Task completed with status: {result.status}")
    if result.result:
        logger.info(f"Analysis result: {result.result}")


async def example_complex_workflow(orchestrator: AgentOrchestrator, logger: logging.Logger):
    """Example of a complex workflow involving multiple agents."""
    logger.info("=== Example 2: Complex Workflow ===")
    
    # Create a complex task that requires multiple capabilities
    task = Task(
        name="comprehensive_code_review",
        description="Perform comprehensive code review including analysis, testing, and documentation",
        task_type="workflow",
        priority=TaskPriority.NORMAL,
        parameters={
            "project_path": str(Path.cwd()),
            "include_tests": True,
            "include_docs": True,
            "output_format": "markdown"
        }
    )
    
    # Execute the workflow
    result = await orchestrator.execute_multi_agent_task(task)
    
    logger.info(f"Workflow completed with status: {result.status}")
    logger.info(f"Execution time: {result.execution_time:.2f} seconds")


async def example_monitoring(orchestrator: AgentOrchestrator, logger: logging.Logger):
    """Example of monitoring the multi-agent system."""
    logger.info("=== Example 3: System Monitoring ===")
    
    # Get multi-agent system status
    status = await orchestrator.get_multi_agent_status()
    
    logger.info("Multi-agent system status:")
    logger.info(f"  Enabled: {status.get('enabled', False)}")
    
    if status.get('enabled'):
        registry_stats = status.get('registry_stats', {})
        logger.info(f"  Total agents: {registry_stats.get('total_agents', 0)}")
        logger.info(f"  Active agents: {registry_stats.get('status_distribution', {}).get('idle', 0)}")
        
        coordination_status = status.get('coordination_status', {})
        logger.info(f"  Active coordinations: {coordination_status.get('active_tasks', 0)}")
        
        mcp_stats = status.get('mcp_stats', {})
        logger.info(f"  MCP servers: {mcp_stats.get('total_servers', 0)}")
        logger.info(f"  Connected servers: {mcp_stats.get('connected_servers', 0)}")
        
        specialized_agents = status.get('specialized_agents', [])
        logger.info(f"  Specialized agents: {', '.join(specialized_agents)}")


async def example_direct_agent_delegation(orchestrator: AgentOrchestrator, logger: logging.Logger):
    """Example of directly delegating tasks to specific agents."""
    logger.info("=== Example 4: Direct Agent Delegation ===")
    
    # Create a documentation task
    doc_task = Task(
        name="generate_readme",
        description="Generate a README file for the project",
        task_type="documentation",
        parameters={
            "project_name": "Multi-Agent Framework",
            "project_description": "A framework for multi-agent collaboration",
            "include_examples": True
        }
    )
    
    # Delegate specifically to the documentation agent
    result = await orchestrator.delegate_task_to_agent(doc_task, "documenter")
    
    logger.info(f"Documentation task completed: {result.status}")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
