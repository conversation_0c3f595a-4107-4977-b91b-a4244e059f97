"""
Comprehensive demonstration of advanced AI agent coding capabilities.

This example showcases all the new advanced features including:
- Advanced code analysis and understanding
- Robust code generation with validation
- Automatic bug fixing loops
- Comprehensive evaluation cycles
- Intelligent debugging and error resolution
"""

import asyncio
import logging
from pathlib import Path

from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_advanced_code_analysis():
    """Demonstrate advanced code analysis capabilities."""
    print("\n" + "="*60)
    print("DEMO: Advanced Code Analysis")
    print("="*60)
    
    # Sample code to analyze
    sample_code = '''
import os
import sys
from typing import Dict, List, Optional

class DataProcessor:
    """Process and validate data."""
    
    def __init__(self, config: Dict[str, str]):
        self.config = config
        self.processed_count = 0
    
    def process_data(self, data: List[Dict]) -> List[Dict]:
        """Process a list of data items."""
        results = []
        for item in data:
            if self.validate_item(item):
                processed = self.transform_item(item)
                results.append(processed)
                self.processed_count += 1
        return results
    
    def validate_item(self, item: Dict) -> bool:
        """Validate a single data item."""
        required_fields = ['id', 'name', 'value']
        for field in required_fields:
            if field not in item:
                return False
        return True
    
    def transform_item(self, item: Dict) -> Dict:
        """Transform a data item."""
        transformed = item.copy()
        transformed['processed'] = True
        transformed['timestamp'] = '2024-01-01'  # Hardcoded for demo
        return transformed
'''
    
    # Initialize advanced orchestrator
    capabilities = AgentCapabilities(
        enable_automatic_evaluation=True,
        enable_advanced_code_generation=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    
    # Perform comprehensive analysis
    analysis_result = await orchestrator.comprehensive_code_analysis(
        code_content=sample_code,
        file_path="demo/data_processor.py",
        analysis_depth="comprehensive"
    )
    
    if analysis_result["success"]:
        context = analysis_result["context"]
        evaluation = analysis_result["evaluation"]
        
        print(f"✅ Analysis completed successfully!")
        print(f"📊 Quality Score: {analysis_result['quality_score']:.2f}")
        print(f"🏗️  Classes found: {len(context.classes)}")
        print(f"🔧 Functions found: {len(context.functions)}")
        print(f"📈 Complexity: {context.complexity_metrics.get('cyclomatic_complexity', 0)}")
        
        if evaluation:
            print(f"🎯 Overall Quality: {evaluation.overall_quality.value}")
            print(f"⚠️  Issues found: {len(evaluation.critical_issues)}")
        
        print("\n📝 Improvement Suggestions:")
        for suggestion in analysis_result["suggestions"][:3]:
            print(f"  • {suggestion}")
    else:
        print(f"❌ Analysis failed: {analysis_result['error']}")


async def demo_advanced_code_generation():
    """Demonstrate advanced code generation capabilities."""
    print("\n" + "="*60)
    print("DEMO: Advanced Code Generation")
    print("="*60)

    capabilities = AgentCapabilities(
        enable_advanced_code_generation=True,
        enable_comprehensive_testing=True,
        enable_automatic_evaluation=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    
    # Define requirements for new code
    requirements = {
        "type": "function",
        "name": "calculate_statistics",
        "description": "Calculate statistical metrics for a dataset",
        "parameters": ["data", "metrics"],
        "return_type": "Dict[str, float]",
        "async": True,
        "error_handling": True,
        "validation": True
    }
    
    # Generate advanced code implementation
    result = await orchestrator.advanced_code_implementation(
        requirements=requirements,
        file_path="demo/statistics.py",
        existing_code=None
    )
    
    if result["success"]:
        print("✅ Code generation successful!")
        
        generated_code = result["generated_code"]
        if hasattr(generated_code, 'code'):
            print("\n📄 Generated Code:")
            print("-" * 40)
            print(generated_code.code[:500] + "..." if len(generated_code.code) > 500 else generated_code.code)
        
        if result.get("test_code"):
            print("\n🧪 Generated Tests:")
            print("-" * 40)
            test_preview = result["test_code"][:300] + "..." if len(result["test_code"]) > 300 else result["test_code"]
            print(test_preview)
        
        if result.get("evaluation"):
            evaluation = result["evaluation"]
            print(f"\n📊 Code Quality: {evaluation.overall_quality.value}")
            print(f"🎯 Score: {evaluation.overall_score:.2f}")
            
            if evaluation.improvement_plan:
                print("\n💡 Improvement Plan:")
                for improvement in evaluation.improvement_plan[:2]:
                    print(f"  • {improvement}")
    else:
        print(f"❌ Code generation failed: {result['error']}")


async def demo_automatic_bug_fixing():
    """Demonstrate automatic bug fixing capabilities."""
    print("\n" + "="*60)
    print("DEMO: Automatic Bug Fixing")
    print("="*60)
    
    # Buggy code sample
    buggy_code = '''
def divide_numbers(a, b):
    """Divide two numbers."""
    result = a / b  # Bug: No zero division check
    return result

def process_list(items):
    """Process a list of items."""
    results = []
    for item in items:
        if item.type == 'number':  # Bug: AttributeError if item has no 'type'
            results.append(divide_numbers(item.value, item.divisor))
    return results

# Test the buggy code
data = [
    {'value': 10, 'divisor': 2},  # Missing 'type' attribute
    {'value': 20, 'divisor': 0, 'type': 'number'}  # Division by zero
]
result = process_list(data)
'''
    
    capabilities = AgentCapabilities(
        enable_automatic_bug_fixing=True,
        enable_automatic_evaluation=True,
        max_fix_iterations=3
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    
    # Simulate an error
    try:
        # This would normally execute the buggy code and catch the error
        # For demo purposes, we'll create a simulated error
        error = AttributeError("'dict' object has no attribute 'type'")
        
        print("🐛 Simulating bug detection...")
        print(f"Error: {error}")
        
        # Attempt automatic bug fixing
        fix_result = await orchestrator.automatic_bug_fixing(
            error=error,
            code_content=buggy_code,
            file_path="demo/buggy_code.py",
            test_files=None  # No test files for this demo
        )
        
        if fix_result["success"]:
            session = fix_result["session"]
            print("✅ Bug fixing successful!")
            print(f"🔄 Fix attempts made: {len(session.fix_attempts)}")
            print(f"⏱️  Total time: {session.total_time:.2f} seconds")
            
            # Show successful fix attempt
            successful_attempts = [a for a in session.fix_attempts if a.status.value == "success"]
            if successful_attempts:
                attempt = successful_attempts[0]
                print(f"🎯 Successful fix: {attempt.suggestion.description}")
            
            if fix_result.get("evaluation"):
                evaluation = fix_result["evaluation"]
                print(f"📊 Post-fix quality: {evaluation.overall_quality.value}")
        else:
            print(f"❌ Automatic bug fixing failed")
            print(f"Session status: {fix_result.get('session', {}).get('final_status', 'unknown')}")
            
    except Exception as e:
        print(f"❌ Demo error: {e}")


async def demo_comprehensive_evaluation():
    """Demonstrate comprehensive evaluation cycles."""
    print("\n" + "="*60)
    print("DEMO: Comprehensive Evaluation Cycles")
    print("="*60)
    
    # Code sample for evaluation
    evaluation_code = '''
import hashlib
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class UserManager:
    """Manage user accounts and authentication."""
    
    def __init__(self):
        self.users = {}
        self.failed_attempts = {}
    
    def create_user(self, username: str, password: str, email: str) -> bool:
        """Create a new user account."""
        try:
            if username in self.users:
                logger.warning(f"User {username} already exists")
                return False
            
            # Hash password (simplified for demo)
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            self.users[username] = {
                'password_hash': password_hash,
                'email': email,
                'active': True,
                'created_at': '2024-01-01'
            }
            
            logger.info(f"User {username} created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user {username}: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate a user."""
        if username not in self.users:
            return None
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        user = self.users[username]
        
        if user['password_hash'] == password_hash and user['active']:
            # Reset failed attempts on successful login
            self.failed_attempts.pop(username, None)
            return user
        else:
            # Track failed attempts
            self.failed_attempts[username] = self.failed_attempts.get(username, 0) + 1
            return None
    
    def get_user_stats(self) -> Dict[str, int]:
        """Get user statistics."""
        active_users = sum(1 for user in self.users.values() if user['active'])
        return {
            'total_users': len(self.users),
            'active_users': active_users,
            'failed_attempts': len(self.failed_attempts)
        }
'''
    
    capabilities = AgentCapabilities(
        enable_automatic_evaluation=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    
    # Run comprehensive evaluation
    evaluation_result = await orchestrator.evaluation_cycles.run_evaluation_cycle(
        code_content=evaluation_code,
        file_path="demo/user_manager.py",
        evaluation_types=[
            "static_analysis",
            "code_quality",
            "performance_analysis", 
            "security_scan",
            "complexity_analysis"
        ]
    )
    
    print("✅ Comprehensive evaluation completed!")
    print(f"🎯 Overall Quality: {evaluation_result.overall_quality.value}")
    print(f"📊 Overall Score: {evaluation_result.overall_score:.2f}")
    print(f"⏱️  Evaluation Time: {evaluation_result.total_time:.2f} seconds")
    
    print(f"\n📋 Evaluation Results:")
    for eval_type, result in evaluation_result.evaluations.items():
        status_emoji = "✅" if result.status.value == "completed" else "❌"
        print(f"  {status_emoji} {eval_type}: {result.quality_level.value} ({result.score:.2f})")
    
    if evaluation_result.critical_issues:
        print(f"\n⚠️  Critical Issues ({len(evaluation_result.critical_issues)}):")
        for issue in evaluation_result.critical_issues[:3]:
            print(f"  • {issue.get('message', 'Unknown issue')}")
    
    if evaluation_result.improvement_plan:
        print(f"\n💡 Improvement Plan:")
        for improvement in evaluation_result.improvement_plan[:3]:
            print(f"  • {improvement}")
    
    if evaluation_result.rollback_recommended:
        print("\n🚨 ROLLBACK RECOMMENDED - Critical issues detected!")


async def demo_full_advanced_cycle():
    """Demonstrate a complete advanced cycle."""
    print("\n" + "="*60)
    print("DEMO: Full Advanced Cycle")
    print("="*60)
    
    # Code that needs enhancement
    legacy_code = '''
def process_data(data):
    results = []
    for item in data:
        if item['status'] == 'active':
            processed = item['value'] * 2
            results.append(processed)
    return results
'''
    
    capabilities = AgentCapabilities(
        enable_automatic_bug_fixing=True,
        enable_automatic_evaluation=True,
        enable_advanced_code_generation=True,
        enable_comprehensive_testing=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    
    # Define enhancement requirements
    requirements = {
        "type": "enhancement",
        "goals": [
            "add_type_hints",
            "add_error_handling",
            "improve_documentation",
            "add_validation"
        ],
        "description": "Enhance legacy data processing function"
    }
    
    # Run full advanced cycle
    results = await orchestrator.run_full_advanced_cycle(
        code_content=legacy_code,
        file_path="demo/legacy_processor.py",
        requirements=requirements
    )
    
    print(f"✅ Full advanced cycle completed!")
    print(f"🎯 Overall Success: {results['overall_success']}")
    
    if results["analysis"]:
        analysis = results["analysis"]
        print(f"📊 Initial Quality Score: {analysis['quality_score']:.2f}")
    
    if results["implementation"]:
        impl = results["implementation"]
        print(f"🔧 Implementation Success: {impl['success']}")
    
    if results["evaluation"]:
        evaluation = results["evaluation"]
        print(f"📈 Final Quality: {evaluation.overall_quality.value}")
        print(f"📊 Final Score: {evaluation.overall_score:.2f}")
    
    if results.get("error"):
        print(f"❌ Error occurred: {results['error']}")


async def main():
    """Run all advanced capability demos."""
    print("🚀 Advanced AI Agent Coding Capabilities Demo")
    print("=" * 60)

    try:
        # Run all demos
        await demo_advanced_code_analysis()
        await demo_advanced_code_generation()
        await demo_automatic_bug_fixing()
        await demo_comprehensive_evaluation()
        await demo_full_advanced_cycle()
        
        print("\n" + "="*60)
        print("✅ All demos completed successfully!")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
