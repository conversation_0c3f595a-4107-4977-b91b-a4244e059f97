#!/usr/bin/env python3
"""
Comprehensive Tutorial: Advanced AI Agent Framework

This tutorial demonstrates all major features of the advanced AI agent framework
through practical examples and step-by-step walkthroughs.
"""

import asyncio
import tempfile
import os
from pathlib import Path

# Import the advanced agent framework
from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)


async def tutorial_1_basic_setup():
    """Tutorial 1: Basic Setup and Configuration"""
    print("\n" + "="*60)
    print("TUTORIAL 1: Basic Setup and Configuration")
    print("="*60)
    
    # Step 1: Configure capabilities
    print("\n📋 Step 1: Configuring Agent Capabilities")
    capabilities = AgentCapabilities(
        enable_automatic_bug_fixing=True,
        enable_automatic_evaluation=True,
        enable_advanced_code_generation=True,
        enable_comprehensive_testing=True,
        max_fix_iterations=3,
        evaluation_on_every_change=True,
        rollback_on_critical_issues=True
    )
    print("✅ Capabilities configured with all advanced features enabled")
    
    # Step 2: Initialize orchestrator
    print("\n🚀 Step 2: Initializing Advanced Orchestrator")
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
    print("✅ Advanced orchestrator initialized successfully")
    
    # Step 3: Check status
    print("\n📊 Step 3: Checking System Status")
    status = await orchestrator.get_advanced_status()
    print(f"✅ System status retrieved:")
    print(f"   - Automatic bug fixing: {status['advanced_capabilities']['automatic_bug_fixing']}")
    print(f"   - Automatic evaluation: {status['advanced_capabilities']['automatic_evaluation']}")
    print(f"   - Advanced code generation: {status['advanced_capabilities']['advanced_code_generation']}")
    print(f"   - Comprehensive testing: {status['advanced_capabilities']['comprehensive_testing']}")
    
    return orchestrator


async def tutorial_2_code_analysis(orchestrator):
    """Tutorial 2: Comprehensive Code Analysis"""
    print("\n" + "="*60)
    print("TUTORIAL 2: Comprehensive Code Analysis")
    print("="*60)
    
    # Sample code with various issues
    sample_code = '''
def calculate_statistics(data):
    # Missing type hints and error handling
    total = 0
    count = 0
    for item in data:
        total += item
        count += 1
    
    average = total / count  # Potential division by zero
    
    # Inefficient calculation
    variance = 0
    for item in data:
        variance += (item - average) ** 2
    variance = variance / count
    
    return average, variance
'''
    
    print("\n📝 Sample Code to Analyze:")
    print(sample_code)
    
    print("\n🔍 Step 1: Running Comprehensive Analysis")
    analysis_result = await orchestrator.comprehensive_code_analysis(
        code_content=sample_code,
        file_path="statistics.py",
        analysis_depth="comprehensive"
    )
    
    if analysis_result["success"]:
        print("✅ Analysis completed successfully!")
        print(f"📊 Quality Score: {analysis_result['quality_score']:.2f}/10")
        
        context = analysis_result["context"]
        print(f"🔧 Complexity Metrics: {context.complexity_metrics}")
        print(f"⚠️  Issues Found: {len(context.potential_issues)}")
        
        if context.potential_issues:
            print("\n🚨 Identified Issues:")
            for i, issue in enumerate(context.potential_issues[:3], 1):
                print(f"   {i}. {issue}")
        
        if context.patterns:
            print(f"\n🎯 Design Patterns Detected: {len(context.patterns)}")
            for pattern in context.patterns[:2]:
                print(f"   - {pattern}")
    
    return analysis_result


async def tutorial_3_code_implementation(orchestrator):
    """Tutorial 3: Advanced Code Implementation"""
    print("\n" + "="*60)
    print("TUTORIAL 3: Advanced Code Implementation")
    print("="*60)
    
    print("\n🛠️  Step 1: Defining Implementation Requirements")
    requirements = {
        "type": "function",
        "name": "validate_and_process_email",
        "description": "Validate email format and extract domain information",
        "parameters": ["email_address"],
        "return_type": "Dict[str, Any]",
        "features": [
            "email_validation",
            "domain_extraction",
            "error_handling",
            "type_hints",
            "comprehensive_documentation"
        ]
    }
    
    print("✅ Requirements defined:")
    for key, value in requirements.items():
        print(f"   - {key}: {value}")
    
    print("\n⚙️  Step 2: Running Advanced Implementation")
    implementation_result = await orchestrator.advanced_code_implementation(
        requirements=requirements,
        file_path="email_validator.py"
    )
    
    if implementation_result["success"]:
        print("✅ Implementation completed successfully!")
        
        generated_code = implementation_result["generated_code"]
        if hasattr(generated_code, 'code'):
            code_content = generated_code.code
        else:
            code_content = str(generated_code)
        
        print(f"\n📝 Generated Code Preview:")
        print("```python")
        print(code_content[:500] + "..." if len(code_content) > 500 else code_content)
        print("```")
        
        if "validation" in implementation_result:
            validation = implementation_result["validation"]
            print(f"\n✅ Validation Results:")
            print(f"   - Syntax Valid: {validation.get('syntax_valid', 'N/A')}")
            print(f"   - Requirements Met: {validation.get('requirements_met', 'N/A')}")
        
        if "evaluation" in implementation_result:
            evaluation = implementation_result["evaluation"]
            print(f"\n📊 Quality Evaluation:")
            print(f"   - Overall Score: {evaluation.overall_score:.2f}/10")
            print(f"   - Quality Level: {evaluation.overall_quality}")
    
    return implementation_result


async def tutorial_4_bug_fixing(orchestrator):
    """Tutorial 4: Automatic Bug Fixing"""
    print("\n" + "="*60)
    print("TUTORIAL 4: Automatic Bug Fixing")
    print("="*60)
    
    # Buggy code example
    buggy_code = '''
def divide_and_process(numbers, divisor):
    results = []
    for num in numbers:
        result = num / divisor  # Bug: No zero division check
        results.append(result)
    
    # Bug: Accessing undefined variable
    average = sum(results) / len(result)  # 'result' should be 'results'
    
    return results, average
'''
    
    print("\n🐛 Buggy Code Example:")
    print(buggy_code)
    
    # Create a temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(buggy_code)
        temp_file = f.name
    
    try:
        print("\n🔧 Step 1: Running Automatic Bug Detection and Fixing")
        
        # Note: This would typically use the bug fix loop, but for demo purposes
        # we'll show how it would work conceptually
        print("🔍 Analyzing code for bugs...")
        analysis_result = await orchestrator.comprehensive_code_analysis(
            code_content=buggy_code,
            file_path=temp_file,
            analysis_depth="comprehensive"
        )
        
        if analysis_result["success"]:
            context = analysis_result["context"]
            print(f"✅ Bug analysis completed")
            print(f"⚠️  Issues detected: {len(context.potential_issues)}")
            
            if context.potential_issues:
                print("\n🚨 Bugs Found:")
                for i, issue in enumerate(context.potential_issues, 1):
                    print(f"   {i}. {issue}")
        
        print("\n🛠️  Step 2: Generating Fixed Version")
        fix_requirements = {
            "type": "enhancement",
            "goals": ["fix_bugs", "add_error_handling", "improve_safety"],
            "description": "Fix identified bugs and improve code safety"
        }
        
        fixed_result = await orchestrator.advanced_code_implementation(
            requirements=fix_requirements,
            file_path=temp_file,
            existing_code=buggy_code
        )
        
        if fixed_result["success"]:
            print("✅ Bug fixing completed!")
            print("📝 Fixed code generated with proper error handling")
    
    finally:
        # Clean up temporary file
        os.unlink(temp_file)


async def tutorial_5_full_cycle(orchestrator):
    """Tutorial 5: Complete Advanced Cycle"""
    print("\n" + "="*60)
    print("TUTORIAL 5: Complete Advanced Cycle")
    print("="*60)
    
    # Legacy code that needs comprehensive improvement
    legacy_code = '''
def process_user_data(data):
    result = []
    for item in data:
        if item['age'] > 18:
            item['status'] = 'adult'
        else:
            item['status'] = 'minor'
        result.append(item)
    return result
'''
    
    print("\n📜 Legacy Code to Enhance:")
    print(legacy_code)
    
    print("\n🚀 Step 1: Defining Enhancement Requirements")
    enhancement_requirements = {
        "type": "enhancement",
        "goals": [
            "add_type_hints",
            "add_error_handling",
            "improve_documentation",
            "add_validation",
            "modernize_syntax",
            "improve_performance"
        ],
        "description": "Comprehensive modernization of legacy user data processing"
    }
    
    print("✅ Enhancement goals defined:")
    for goal in enhancement_requirements["goals"]:
        print(f"   - {goal}")
    
    print("\n⚙️  Step 2: Running Full Advanced Cycle")
    print("   This includes: Analysis → Implementation → Testing → Evaluation")
    
    results = await orchestrator.run_full_advanced_cycle(
        code_content=legacy_code,
        file_path="user_processor.py",
        requirements=enhancement_requirements
    )
    
    print(f"\n✅ Full cycle completed!")
    print(f"🎯 Overall Success: {results.get('overall_success', False)}")
    
    if results.get("analysis"):
        analysis = results["analysis"]
        print(f"📊 Initial Analysis:")
        print(f"   - Quality Score: {analysis.get('quality_score', 'N/A')}")
        print(f"   - Issues Found: {len(analysis.get('context', {}).get('potential_issues', []))}")
    
    if results.get("implementation"):
        implementation = results["implementation"]
        print(f"🛠️  Implementation Results:")
        print(f"   - Success: {implementation.get('success', False)}")
        if implementation.get("evaluation"):
            eval_result = implementation["evaluation"]
            print(f"   - Final Quality Score: {eval_result.overall_score:.2f}")
            print(f"   - Quality Level: {eval_result.overall_quality}")
    
    return results


async def main():
    """Run the comprehensive tutorial"""
    print("🎓 Welcome to the Advanced AI Agent Framework Tutorial!")
    print("This tutorial will walk you through all major features step by step.")
    
    try:
        # Tutorial 1: Basic Setup
        orchestrator = await tutorial_1_basic_setup()
        
        # Tutorial 2: Code Analysis
        await tutorial_2_code_analysis(orchestrator)
        
        # Tutorial 3: Code Implementation
        await tutorial_3_code_implementation(orchestrator)
        
        # Tutorial 4: Bug Fixing
        await tutorial_4_bug_fixing(orchestrator)
        
        # Tutorial 5: Full Cycle
        await tutorial_5_full_cycle(orchestrator)
        
        print("\n" + "="*60)
        print("🎉 TUTORIAL COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\n📚 What you've learned:")
        print("✅ How to set up and configure the advanced agent framework")
        print("✅ How to perform comprehensive code analysis")
        print("✅ How to implement new code with advanced capabilities")
        print("✅ How to automatically detect and fix bugs")
        print("✅ How to run complete enhancement cycles")
        print("\n🚀 Next Steps:")
        print("- Explore the CLI commands for interactive usage")
        print("- Check out the multi-agent coordination features")
        print("- Try the framework on your own codebase")
        print("- Read the full documentation for advanced features")
        
    except Exception as e:
        print(f"\n❌ Tutorial failed with error: {e}")
        print("Please check your setup and try again.")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
